<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { tv } from 'tailwind-variants';
	import Icon from '$lib/components/ui/Icon.svelte';

	const dropdownVariants = tv({
		base: 'relative inline-block text-left'
	});

	const menuVariants = tv({
		base: 'absolute z-50 mt-2 rounded-md border border-border bg-popover p-1 text-popover-foreground shadow-md',
		variants: {
			position: {
				'bottom-start': 'top-full left-0',
				'bottom-end': 'top-full right-0',
				'top-start': 'bottom-full left-0',
				'top-end': 'bottom-full right-0'
			},
			width: {
				auto: 'w-auto min-w-[8rem]',
				trigger: 'w-full',
				sm: 'w-32',
				md: 'w-48',
				lg: 'w-64',
				xl: 'w-80'
			}
		},
		defaultVariants: {
			position: 'bottom-start',
			width: 'auto'
		}
	});

	interface Props {
		open?: boolean;
		position?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';
		width?: 'auto' | 'trigger' | 'sm' | 'md' | 'lg' | 'xl';
		disabled?: boolean;
		class?: string;
		trigger?: any;
		children?: any;
	}

	let {
		open = $bindable(false),
		position = 'bottom-start',
		width = 'auto',
		disabled = false,
		class: className = '',
		trigger,
		children
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		open: void;
		close: void;
		select: { value: any };
	}>();

	let triggerElement: HTMLButtonElement;
	let menuElement: HTMLDivElement;

	let containerClass = $derived(dropdownVariants({ class: className }));
	let menuClass = $derived(menuVariants({ position, width }));

	const toggleDropdown = () => {
		if (disabled) return;

		open = !open;
		if (open) {
			dispatch('open');
			// Focus first menu item
			setTimeout(() => {
				const firstItem = menuElement?.querySelector('[role="menuitem"]') as HTMLElement;
				firstItem?.focus();
			}, 0);
		} else {
			dispatch('close');
		}
	};

	const closeDropdown = () => {
		if (open) {
			open = false;
			dispatch('close');
			triggerElement?.focus();
		}
	};

	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Escape') {
			closeDropdown();
		} else if (event.key === 'ArrowDown' && !open) {
			event.preventDefault();
			toggleDropdown();
		}
	};

	const handleMenuKeydown = (event: KeyboardEvent) => {
		const items = Array.from(menuElement.querySelectorAll('[role="menuitem"]')) as HTMLElement[];
		const currentIndex = items.indexOf(event.target as HTMLElement);

		switch (event.key) {
			case 'ArrowDown':
				event.preventDefault();
				const nextIndex = (currentIndex + 1) % items.length;
				items[nextIndex]?.focus();
				break;
			case 'ArrowUp':
				event.preventDefault();
				const prevIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
				items[prevIndex]?.focus();
				break;
			case 'Home':
				event.preventDefault();
				items[0]?.focus();
				break;
			case 'End':
				event.preventDefault();
				items[items.length - 1]?.focus();
				break;
			case 'Escape':
				closeDropdown();
				break;
		}
	};

	const handleClickOutside = (event: MouseEvent) => {
		if (
			open &&
			!triggerElement?.contains(event.target as Node) &&
			!menuElement?.contains(event.target as Node)
		) {
			closeDropdown();
		}
	};

	onMount(() => {
		document.addEventListener('click', handleClickOutside);
		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});
</script>

<div class={containerClass}>
	<!-- Trigger -->
	<button
		bind:this={triggerElement}
		type="button"
		class="inline-flex w-full justify-between items-center rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
		aria-expanded={open}
		aria-haspopup="true"
		{disabled}
		onclick={toggleDropdown}
		onkeydown={handleKeydown}
	>
		{@render trigger?.() ?? (() => {
			return `<span>Select option</span>`;
		})()}
		<Icon name="chevron-down" class="h-4 w-4 opacity-50" />
	</button>

	<!-- Menu -->
	{#if open}
		<div
			bind:this={menuElement}
			class={menuClass}
			role="menu"
			aria-orientation="vertical"
			onkeydown={handleMenuKeydown}
		>
			{@render children?.({ closeDropdown })}
		</div>
	{/if}
</div>
