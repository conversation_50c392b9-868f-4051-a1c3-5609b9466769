<script lang="ts">
	import { onMount } from 'svelte';
	import { marked } from 'marked';
	import DOMPurify from 'dompurify';

	interface Props {
		content?: string;
		className?: string;
	}

	const { content = '', className = '' }: Props = $props();

	let renderedHTML = '';

	// 配置 marked
	marked.setOptions({
		breaks: true,
		gfm: true,
		headerIds: true,
		mangle: false
	});

	// 自定義渲染器
	const renderer = new marked.Renderer();

	// 自定義連結渲染，添加外部連結標識
	renderer.link = (href, title, text) => {
		const isExternal = href?.startsWith('http') || href?.startsWith('//');
		const target = isExternal ? ' target="_blank" rel="noopener noreferrer"' : '';
		const titleAttr = title ? ` title="${title}"` : '';
		return `<a href="${href}"${titleAttr}${target}>${text}</a>`;
	};

	// 自定義代碼塊渲染，添加語法高亮
	renderer.code = (code, language) => {
		const lang = language || 'text';
		return `<pre class="code-block"><code class="language-${lang}">${escapeHtml(code)}</code></pre>`;
	};

	// 自定義表格渲染
	renderer.table = (header, body) => {
		return `<div class="table-wrapper"><table class="markdown-table">${header}${body}</table></div>`;
	};

	// 自定義引用渲染
	renderer.blockquote = quote => {
		return `<blockquote class="markdown-blockquote">${quote}</blockquote>`;
	};

	// 自定義任務列表渲染
	renderer.listitem = (text, task, checked) => {
		if (task) {
			const checkedAttr = checked ? ' checked' : '';
			return `<li class="task-list-item"><input type="checkbox" disabled${checkedAttr}> ${text}</li>`;
		}
		return `<li>${text}</li>`;
	};

	marked.use({ renderer });

	function escapeHtml(text: string): string {
		const div = document.createElement('div');
		div.textContent = text;
		return div.innerHTML;
	}

	function renderMarkdown(markdown: string): string {
		try {
			const html = marked(markdown);
			// 使用 DOMPurify 清理 HTML，防止 XSS 攻擊
			return DOMPurify.sanitize(html, {
				ALLOWED_TAGS: [
					'h1',
					'h2',
					'h3',
					'h4',
					'h5',
					'h6',
					'p',
					'br',
					'strong',
					'em',
					'u',
					's',
					'del',
					'a',
					'img',
					'code',
					'pre',
					'ul',
					'ol',
					'li',
					'table',
					'thead',
					'tbody',
					'tr',
					'th',
					'td',
					'blockquote',
					'hr',
					'div',
					'span',
					'input' // 用於任務列表
				],
				ALLOWED_ATTR: [
					'href',
					'title',
					'target',
					'rel',
					'src',
					'alt',
					'width',
					'height',
					'class',
					'id',
					'type',
					'checked',
					'disabled'
				],
				ALLOWED_URI_REGEXP:
					/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|xxx):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i
			});
		} catch (error) {
			console.error('Markdown rendering error:', error);
			return `<p class="error">渲染錯誤: ${error}</p>`;
		}
	}

	$: {
		renderedHTML = renderMarkdown(content);
	}

	onMount(() => {
		// 添加代碼高亮（如果需要）
		// 這裡可以集成 Prism.js 或其他語法高亮庫
	});
</script>

<div class="markdown-renderer {className}">
	{@html renderedHTML}
</div>

<style>
	:global(.markdown-renderer) {
		line-height: 1.7;
		color: hsl(var(--foreground));
	}

	/* 標題樣式 */
	:global(.markdown-renderer h1) {
		font-size: 2.25rem;
		font-weight: 700;
		margin: 2rem 0 1rem 0;
		line-height: 1.2;
		border-bottom: 2px solid hsl(var(--border));
		padding-bottom: 0.5rem;
	}

	:global(.markdown-renderer h2) {
		font-size: 1.875rem;
		font-weight: 600;
		margin: 1.75rem 0 0.75rem 0;
		line-height: 1.3;
		border-bottom: 1px solid hsl(var(--border));
		padding-bottom: 0.25rem;
	}

	:global(.markdown-renderer h3) {
		font-size: 1.5rem;
		font-weight: 600;
		margin: 1.5rem 0 0.5rem 0;
		line-height: 1.4;
	}

	:global(.markdown-renderer h4) {
		font-size: 1.25rem;
		font-weight: 600;
		margin: 1.25rem 0 0.5rem 0;
		line-height: 1.4;
	}

	:global(.markdown-renderer h5) {
		font-size: 1.125rem;
		font-weight: 600;
		margin: 1rem 0 0.5rem 0;
		line-height: 1.4;
	}

	:global(.markdown-renderer h6) {
		font-size: 1rem;
		font-weight: 600;
		margin: 1rem 0 0.5rem 0;
		line-height: 1.4;
		color: hsl(var(--muted-foreground));
	}

	/* 段落樣式 */
	:global(.markdown-renderer p) {
		margin: 1rem 0;
		line-height: 1.7;
	}

	/* 連結樣式 */
	:global(.markdown-renderer a) {
		color: hsl(var(--primary));
		text-decoration: underline;
		text-decoration-color: hsl(var(--primary) / 0.3);
		text-underline-offset: 2px;
		transition: all 0.2s ease;
	}

	:global(.markdown-renderer a:hover) {
		text-decoration-color: hsl(var(--primary));
	}

	/* 代碼樣式 */
	:global(.markdown-renderer code) {
		background: hsl(var(--muted));
		padding: 0.125rem 0.25rem;
		border-radius: 0.25rem;
		font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
		font-size: 0.875em;
		color: hsl(var(--foreground));
	}

	:global(.markdown-renderer .code-block) {
		background: hsl(var(--muted));
		border: 1px solid hsl(var(--border));
		border-radius: 0.5rem;
		padding: 1rem;
		margin: 1rem 0;
		overflow-x: auto;
	}

	:global(.markdown-renderer .code-block code) {
		background: none;
		padding: 0;
		border-radius: 0;
		font-size: 0.875rem;
		line-height: 1.5;
	}

	/* 列表樣式 */
	:global(.markdown-renderer ul) {
		margin: 1rem 0;
		padding-left: 1.5rem;
	}

	:global(.markdown-renderer ol) {
		margin: 1rem 0;
		padding-left: 1.5rem;
	}

	:global(.markdown-renderer li) {
		margin: 0.25rem 0;
		line-height: 1.6;
	}

	:global(.markdown-renderer .task-list-item) {
		list-style: none;
		margin-left: -1.5rem;
		padding-left: 1.5rem;
	}

	:global(.markdown-renderer .task-list-item input) {
		margin-right: 0.5rem;
	}

	/* 引用樣式 */
	:global(.markdown-renderer .markdown-blockquote) {
		border-left: 4px solid hsl(var(--primary));
		padding-left: 1rem;
		margin: 1rem 0;
		color: hsl(var(--muted-foreground));
		font-style: italic;
		background: hsl(var(--muted) / 0.3);
		padding: 1rem;
		border-radius: 0 0.5rem 0.5rem 0;
	}

	/* 表格樣式 */
	:global(.markdown-renderer .table-wrapper) {
		overflow-x: auto;
		margin: 1rem 0;
	}

	:global(.markdown-renderer .markdown-table) {
		width: 100%;
		border-collapse: collapse;
		border: 1px solid hsl(var(--border));
		border-radius: 0.5rem;
		overflow: hidden;
	}

	:global(.markdown-renderer .markdown-table th) {
		background: hsl(var(--muted));
		padding: 0.75rem;
		text-align: left;
		font-weight: 600;
		border-bottom: 1px solid hsl(var(--border));
	}

	:global(.markdown-renderer .markdown-table td) {
		padding: 0.75rem;
		border-bottom: 1px solid hsl(var(--border));
	}

	:global(.markdown-renderer .markdown-table tr:last-child td) {
		border-bottom: none;
	}

	:global(.markdown-renderer .markdown-table tr:nth-child(even)) {
		background: hsl(var(--muted) / 0.3);
	}

	/* 分隔線樣式 */
	:global(.markdown-renderer hr) {
		border: none;
		height: 1px;
		background: hsl(var(--border));
		margin: 2rem 0;
	}

	/* 圖片樣式 */
	:global(.markdown-renderer img) {
		max-width: 100%;
		height: auto;
		border-radius: 0.5rem;
		margin: 1rem 0;
		box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
	}

	/* 強調樣式 */
	:global(.markdown-renderer strong) {
		font-weight: 700;
		color: hsl(var(--foreground));
	}

	:global(.markdown-renderer em) {
		font-style: italic;
	}

	:global(.markdown-renderer del) {
		text-decoration: line-through;
		color: hsl(var(--muted-foreground));
	}

	/* 錯誤樣式 */
	:global(.markdown-renderer .error) {
		color: hsl(var(--destructive));
		background: hsl(var(--destructive) / 0.1);
		padding: 1rem;
		border-radius: 0.5rem;
		border: 1px solid hsl(var(--destructive) / 0.2);
	}

	/* 響應式設計 */
	@media (max-width: 768px) {
		:global(.markdown-renderer h1) {
			font-size: 1.875rem;
		}

		:global(.markdown-renderer h2) {
			font-size: 1.5rem;
		}

		:global(.markdown-renderer h3) {
			font-size: 1.25rem;
		}

		:global(.markdown-renderer .code-block) {
			padding: 0.75rem;
		}

		:global(.markdown-renderer .markdown-table th),
		:global(.markdown-renderer .markdown-table td) {
			padding: 0.5rem;
		}
	}
</style>
