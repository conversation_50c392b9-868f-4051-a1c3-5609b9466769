<script lang="ts">
	import { onMount } from 'svelte';
	import Icon from '$lib/components/ui/Icon.svelte';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { allNotes } from '$stores/notes';
	import { dependencyService } from '$lib/services/dependencyService';

	// Props
	let { autoRefresh = true, maxSuggestions = 10 } = $props();

	// 智能建議類型
	interface SmartSuggestion {
		id: string;
		type: 'connect' | 'disconnect' | 'merge' | 'split' | 'tag' | 'reorganize';
		priority: 'low' | 'medium' | 'high';
		title: string;
		description: string;
		confidence: number; // 0-1
		sourceNoteId?: string;
		targetNoteId?: string;
		sourceTitle?: string;
		targetTitle?: string;
		reasoning: string;
		actionText: string;
		automated: boolean;
	}

	// 狀態
	let suggestions: SmartSuggestion[] = $state([]);
	let isGenerating = $state(false);
	let lastGenerated: Date | null = $state(null);
	let appliedSuggestions = $state(new Set<string>());

	onMount(() => {
		if ($allNotes && $allNotes.length > 0) {
			generateSuggestions();
		}
	});

	const generateSuggestions = async () => {
		if (!$allNotes || $allNotes.length === 0) return;

		isGenerating = true;
		try {
			const graph = await dependencyService.analyzeDependencies($allNotes);
			const newSuggestions: SmartSuggestion[] = [];

			// 生成連接建議
			const connectionSuggestions = await generateConnectionSuggestions();
			newSuggestions.push(...connectionSuggestions);

			// 生成合併建議
			const mergeSuggestions = await generateMergeSuggestions();
			newSuggestions.push(...mergeSuggestions);

			// 生成標籤建議
			const tagSuggestions = await generateTagSuggestions();
			newSuggestions.push(...tagSuggestions);

			// 生成重組建議
			const reorganizeSuggestions = await generateReorganizeSuggestions();
			newSuggestions.push(...reorganizeSuggestions);

			// 按優先級和信心度排序
			suggestions = newSuggestions
				.sort((a, b) => {
					const priorityOrder = { high: 3, medium: 2, low: 1 };
					const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
					if (priorityDiff !== 0) return priorityDiff;
					return b.confidence - a.confidence;
				})
				.slice(0, maxSuggestions);

			lastGenerated = new Date();
		} catch (error) {
			console.error('Failed to generate suggestions:', error);
		} finally {
			isGenerating = false;
		}
	};

	const generateConnectionSuggestions = async (): Promise<SmartSuggestion[]> => {
		const suggestions: SmartSuggestion[] = [];

		// 找到內容相似但未連接的筆記
		for (let i = 0; i < $allNotes.length; i++) {
			for (let j = i + 1; j < $allNotes.length; j++) {
				const note1 = $allNotes[i];
				const note2 = $allNotes[j];

				const similarity = calculateContentSimilarity(note1, note2);
				if (similarity > 0.6) {
					const hasConnection = await checkExistingConnection(note1.id, note2.id);
					if (!hasConnection) {
						suggestions.push({
							id: `connect-${note1.id}-${note2.id}`,
							type: 'connect',
							priority: similarity > 0.8 ? 'high' : 'medium',
							title: '建議建立連接',
							description: `「${note1.title}」和「${note2.title}」內容相似`,
							confidence: similarity,
							sourceNoteId: note1.id,
							targetNoteId: note2.id,
							sourceTitle: note1.title,
							targetTitle: note2.title,
							reasoning: `內容相似度：${(similarity * 100).toFixed(1)}%，建議建立連接以增強知識關聯`,
							actionText: '建立連接',
							automated: true
						});
					}
				}
			}
		}

		return suggestions;
	};

	const generateMergeSuggestions = async (): Promise<SmartSuggestion[]> => {
		const suggestions: SmartSuggestion[] = [];

		// 找到高度重複的筆記
		for (let i = 0; i < $allNotes.length; i++) {
			for (let j = i + 1; j < $allNotes.length; j++) {
				const note1 = $allNotes[i];
				const note2 = $allNotes[j];

				const similarity = calculateContentSimilarity(note1, note2);
				const titleSimilarity = calculateTitleSimilarity(note1.title, note2.title);

				if (similarity > 0.85 && titleSimilarity > 0.7) {
					suggestions.push({
						id: `merge-${note1.id}-${note2.id}`,
						type: 'merge',
						priority: 'medium',
						title: '建議合併筆記',
						description: `「${note1.title}」和「${note2.title}」內容高度重複`,
						confidence: (similarity + titleSimilarity) / 2,
						sourceNoteId: note1.id,
						targetNoteId: note2.id,
						sourceTitle: note1.title,
						targetTitle: note2.title,
						reasoning: `內容相似度：${(similarity * 100).toFixed(1)}%，標題相似度：${(titleSimilarity * 100).toFixed(1)}%`,
						actionText: '合併筆記',
						automated: false
					});
				}
			}
		}

		return suggestions;
	};

	const generateTagSuggestions = async (): Promise<SmartSuggestion[]> => {
		const suggestions: SmartSuggestion[] = [];

		// 分析標籤使用模式
		const tagPatterns = analyzeTagPatterns();

		for (const note of $allNotes) {
			const suggestedTags = suggestTagsForNote(note, tagPatterns);

			if (suggestedTags.length > 0) {
				suggestions.push({
					id: `tag-${note.id}`,
					type: 'tag',
					priority: 'low',
					title: '建議添加標籤',
					description: `為「${note.title}」添加相關標籤`,
					confidence: 0.7,
					sourceNoteId: note.id,
					sourceTitle: note.title,
					reasoning: `基於內容分析，建議添加標籤：${suggestedTags.join(', ')}`,
					actionText: '添加標籤',
					automated: true
				});
			}
		}

		return suggestions;
	};

	const generateReorganizeSuggestions = async (): Promise<SmartSuggestion[]> => {
		const suggestions: SmartSuggestion[] = [];

		// 檢測組織問題
		const orphanedNotes = findOrphanedNotes();
		const overConnectedNotes = findOverConnectedNotes();

		if (orphanedNotes.length > 0) {
			suggestions.push({
				id: 'reorganize-orphaned',
				type: 'reorganize',
				priority: 'medium',
				title: '整理孤立筆記',
				description: `發現 ${orphanedNotes.length} 個孤立筆記`,
				confidence: 0.8,
				reasoning: '孤立筆記可能降低知識發現能力，建議建立適當的連接',
				actionText: '整理孤立筆記',
				automated: false
			});
		}

		if (overConnectedNotes.length > 0) {
			suggestions.push({
				id: 'reorganize-overconnected',
				type: 'reorganize',
				priority: 'low',
				title: '簡化過度連接',
				description: `發現 ${overConnectedNotes.length} 個過度連接的筆記`,
				confidence: 0.6,
				reasoning: '過度連接可能導致信息過載，建議精簡連接',
				actionText: '簡化連接',
				automated: false
			});
		}

		return suggestions;
	};

	const applySuggestion = async (suggestion: SmartSuggestion) => {
		try {
			switch (suggestion.type) {
				case 'connect':
					await handleConnectSuggestion(suggestion);
					break;
				case 'tag':
					await handleTagSuggestion(suggestion);
					break;
				default:
					console.log('Manual action required for:', suggestion.type);
			}

			appliedSuggestions.add(suggestion.id);
			suggestions = suggestions.filter(s => s.id !== suggestion.id);
		} catch (error) {
			console.error('Failed to apply suggestion:', error);
		}
	};

	const dismissSuggestion = (suggestionId: string) => {
		suggestions = suggestions.filter(s => s.id !== suggestionId);
	};

	const handleConnectSuggestion = async (suggestion: SmartSuggestion) => {
		// 這裡可以實現自動建立連接的邏輯
		console.log('Connecting notes:', suggestion.sourceNoteId, suggestion.targetNoteId);
	};

	const handleTagSuggestion = async (suggestion: SmartSuggestion) => {
		// 這裡可以實現自動添加標籤的邏輯
		console.log('Adding tags to note:', suggestion.sourceNoteId);
	};

	// 輔助函數
	const calculateContentSimilarity = (note1: any, note2: any): number => {
		const words1 = new Set(note1.content.toLowerCase().split(/\s+/));
		const words2 = new Set(note2.content.toLowerCase().split(/\s+/));
		const intersection = new Set([...words1].filter(word => words2.has(word)));
		const union = new Set([...words1, ...words2]);
		return union.size > 0 ? intersection.size / union.size : 0;
	};

	const calculateTitleSimilarity = (title1: string, title2: string): number => {
		const words1 = new Set(title1.toLowerCase().split(/\s+/));
		const words2 = new Set(title2.toLowerCase().split(/\s+/));
		const intersection = new Set([...words1].filter(word => words2.has(word)));
		const union = new Set([...words1, ...words2]);
		return union.size > 0 ? intersection.size / union.size : 0;
	};

	const checkExistingConnection = async (noteId1: string, noteId2: string): Promise<boolean> => {
		// 檢查是否已存在連接
		return false; // 簡化實現
	};

	const analyzeTagPatterns = () => {
		// 分析標籤使用模式
		return {};
	};

	const suggestTagsForNote = (note: any, patterns: any): string[] => {
		// 為筆記建議標籤
		return [];
	};

	const findOrphanedNotes = () => {
		// 找到孤立筆記
		return [];
	};

	const findOverConnectedNotes = () => {
		// 找到過度連接的筆記
		return [];
	};

	const getSuggestionIcon = (type: string) => {
		switch (type) {
			case 'connect':
				return 'link';
			case 'disconnect':
				return 'unlink';
			case 'merge':
				return 'merge';
			case 'split':
				return 'split';
			case 'tag':
				return 'tag';
			default:
				return 'lightbulb';
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'high':
				return 'text-red-600 bg-red-50 border-red-200';
			case 'medium':
				return 'text-orange-600 bg-orange-50 border-orange-200';
			case 'low':
				return 'text-blue-600 bg-blue-50 border-blue-200';
			default:
				return 'text-gray-600 bg-gray-50 border-gray-200';
		}
	};
</script>

<Card class="p-6">
	<div class="flex items-center justify-between mb-4">
		<div class="flex items-center space-x-2">
			<Icon name="lightbulb" class="h-5 w-5 text-yellow-500" />
			<h3 class="text-lg font-semibold">智能建議</h3>
		</div>

		<div class="flex items-center space-x-2">
			{#if lastGenerated}
				<span class="text-sm text-muted-foreground">
					{lastGenerated.toLocaleTimeString()}
				</span>
			{/if}
			<Button variant="outline" size="sm" onclick={generateSuggestions} disabled={isGenerating}>
				<Icon name="refresh-cw" class="h-4 w-4 mr-1 {isGenerating ? 'animate-spin' : ''}" />
				刷新
			</Button>
		</div>
	</div>

	{#if isGenerating}
		<div class="flex items-center justify-center py-8">
			<Icon name="refresh-cw" class="h-6 w-6 animate-spin text-primary mr-2" />
			<span>正在生成智能建議...</span>
		</div>
	{:else if suggestions.length === 0}
		<div class="text-center py-8">
			<Icon name="check-circle" class="h-12 w-12 text-green-500 mx-auto mb-3" />
			<h4 class="font-medium text-green-700 mb-1">系統運行良好</h4>
			<p class="text-muted-foreground">暫無優化建議</p>
		</div>
	{:else}
		<div class="space-y-3">
			{#each suggestions as suggestion}
				<div class="border rounded-lg p-4 {getPriorityColor(suggestion.priority)}">
					<div class="flex items-start justify-between">
						<div class="flex items-start space-x-3 flex-1">
							<Icon
								name={getSuggestionIcon(suggestion.type)}
								class="h-5 w-5 mt-0.5 flex-shrink-0"
							/>

							<div class="flex-1">
								<div class="flex items-center space-x-2 mb-1">
									<h4 class="font-medium">{suggestion.title}</h4>
									<span class="px-2 py-1 text-xs rounded-full bg-white/50">
										{suggestion.priority}
									</span>
									<span class="px-2 py-1 text-xs rounded-full bg-white/50">
										{(suggestion.confidence * 100).toFixed(0)}% 信心度
									</span>
								</div>

								<p class="text-sm mb-2">{suggestion.description}</p>

								{#if suggestion.sourceTitle && suggestion.targetTitle}
									<div class="flex items-center space-x-2 text-sm font-medium mb-2">
										<span class="px-2 py-1 bg-white/70 rounded">{suggestion.sourceTitle}</span>
										<Icon name="arrow-right" class="h-3 w-3" />
										<span class="px-2 py-1 bg-white/70 rounded">{suggestion.targetTitle}</span>
									</div>
								{/if}

								<p class="text-xs text-muted-foreground">{suggestion.reasoning}</p>
							</div>
						</div>

						<div class="flex items-center space-x-1 ml-3">
							{#if suggestion.automated}
								<Button size="sm" variant="default" onclick={() => applySuggestion(suggestion)}>
									{suggestion.actionText}
								</Button>
							{:else}
								<Button size="sm" variant="outline">查看詳情</Button>
							{/if}

							<Button size="sm" variant="ghost" onclick={() => dismissSuggestion(suggestion.id)}>
								<Icon name="x" class="h-3 w-3" />
							</Button>
						</div>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</Card>
