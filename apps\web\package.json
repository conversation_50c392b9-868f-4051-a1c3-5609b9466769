{"name": "@life-note/web", "version": "0.1.0", "description": "Life Note Web Application - Svelte + SvelteKit Frontend", "private": true, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "lint:fix": "prettier --write . && eslint . --fix", "format": "prettier --write .", "typecheck": "svelte-check --tsconfig ./tsconfig.json"}, "dependencies": {"@codemirror/autocomplete": "^6.18.3", "@codemirror/commands": "^6.7.1", "@codemirror/lang-markdown": "^6.3.1", "@codemirror/search": "^6.5.8", "@codemirror/state": "^6.4.1", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.34.2", "@life-note/storage": "workspace:*", "@modelcontextprotocol/sdk": "^1.13.2", "@tauri-apps/api": "^2.1.1", "codemirror": "^6.0.1", "d3": "^7.9.0", "dompurify": "^3.2.6", "fuse.js": "^7.0.0", "highlight.js": "^11.10.0", "lucide-svelte": "^0.525.0", "marked": "^15.0.12", "mode-watcher": "^0.4.1", "svelte-french-toast": "^1.2.0", "tailwind-merge": "^2.5.5", "tailwind-variants": "^0.2.1", "zod": "^3.23.8"}, "devDependencies": {"@playwright/test": "^1.49.1", "@sveltejs/adapter-auto": "^3.3.1", "@sveltejs/adapter-static": "^3.0.6", "@sveltejs/kit": "^2.9.1", "@sveltejs/vite-plugin-svelte": "^4.0.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/d3": "^7.4.3", "@types/dompurify": "^3.2.0", "@types/marked": "^6.0.0", "@types/node": "^20.19.2", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.46.0", "postcss": "^8.5.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.2", "svelte": "^5.14.2", "svelte-check": "^4.0.8", "tailwindcss": "^3.4.17", "tslib": "^2.8.1", "typescript": "^5.6.3", "vite": "^6.0.3", "vitest": "^1.6.1"}, "keywords": ["svelte", "sveltekit", "typescript", "tailwindcss", "markdown", "note-taking", "frontend"], "author": "Life Note Team", "license": "MIT"}