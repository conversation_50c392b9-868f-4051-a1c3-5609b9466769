<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import {
		Search,
		Filter,
		X,
		Calendar,
		Tag,
		FolderOpen,
		SortAsc,
		SortDesc,
		Grid,
		List,
		Clock,
		FileText
	} from 'lucide-svelte';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import SearchFilters from '$components/search/SearchFilters.svelte';
	import SearchResults from '$components/search/SearchResults.svelte';
	import { noteStore, filteredNotes } from '$stores/note';

	// Search state
	let searchQuery = '';
	let showFilters = false;
	let viewMode: 'grid' | 'list' = 'grid';
	let sortBy: 'relevance' | 'date' | 'title' | 'category' = 'relevance';
	let sortOrder: 'asc' | 'desc' = 'desc';

	// Filter state
	let selectedCategories: string[] = [];
	let selectedTags: string[] = [];
	let selectedStatus: string[] = [];
	let selectedPriority: string[] = [];
	let dateRange: { start: Date | null; end: Date | null } = { start: null, end: null };

	// Results state
	let isSearching = false;
	let searchResults: any[] = [];
	let totalResults = 0;
	let searchTime = 0;

	// Reactive values
	const hasActiveFilters = $derived(
		selectedCategories.length > 0 ||
		selectedTags.length > 0 ||
		selectedStatus.length > 0 ||
		selectedPriority.length > 0 ||
		dateRange.start ||
		dateRange.end
	);

	const hasQuery = $derived(searchQuery.trim().length > 0);

	// Initialize from URL params
	onMount(() => {
		const urlParams = new URLSearchParams($page.url.search);
		searchQuery = urlParams.get('q') || '';

		if (searchQuery) {
			performSearch();
		}
	});

	// Perform search
	const performSearch = async () => {
		if (!hasQuery && !hasActiveFilters) {
			searchResults = [];
			totalResults = 0;
			return;
		}

		isSearching = true;
		const startTime = performance.now();

		try {
			// Update URL
			const params = new URLSearchParams();
			if (searchQuery) params.set('q', searchQuery);
			if (selectedCategories.length) params.set('categories', selectedCategories.join(','));
			if (selectedTags.length) params.set('tags', selectedTags.join(','));
			if (selectedStatus.length) params.set('status', selectedStatus.join(','));
			if (selectedPriority.length) params.set('priority', selectedPriority.join(','));

			const newUrl = `/search${params.toString() ? '?' + params.toString() : ''}`;
			goto(newUrl, { replaceState: true, noScroll: true });

			// Apply filters to note store
			noteStore.setSearchQuery(searchQuery);
			// TODO: Apply other filters

			// Simulate search delay
			await new Promise(resolve => setTimeout(resolve, 300));

			// Get filtered results
			searchResults = $filteredNotes;
			totalResults = searchResults.length;
			searchTime = performance.now() - startTime;
		} catch (error) {
			console.error('Search error:', error);
			searchResults = [];
			totalResults = 0;
		} finally {
			isSearching = false;
		}
	};

	// Handle search input
	const handleSearchInput = (event: Event) => {
		const target = event.target as HTMLInputElement;
		searchQuery = target.value;

		// Debounce search
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(performSearch, 300);
	};

	let searchTimeout: NodeJS.Timeout;

	// Handle filter changes
	const handleFiltersChange = (event: CustomEvent) => {
		const { categories, tags, status, priority, dateRange: newDateRange } = event.detail;

		selectedCategories = categories;
		selectedTags = tags;
		selectedStatus = status;
		selectedPriority = priority;
		dateRange = newDateRange;

		performSearch();
	};

	// Clear all filters
	const clearAllFilters = () => {
		searchQuery = '';
		selectedCategories = [];
		selectedTags = [];
		selectedStatus = [];
		selectedPriority = [];
		dateRange = { start: null, end: null };

		noteStore.clearFilters();
		searchResults = [];
		totalResults = 0;

		goto('/search', { replaceState: true });
	};

	// Handle sort change
	const handleSortChange = (newSortBy: typeof sortBy) => {
		if (sortBy === newSortBy) {
			sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
		} else {
			sortBy = newSortBy;
			sortOrder = newSortBy === 'date' ? 'desc' : 'asc';
		}

		// TODO: Apply sorting to results
		performSearch();
	};

	// Handle view mode change
	const toggleViewMode = () => {
		viewMode = viewMode === 'grid' ? 'list' : 'grid';
	};

	// Handle note click
	const handleNoteClick = (noteId: string) => {
		goto(`/notes/${noteId}`);
	};

	// Keyboard shortcuts
	const handleKeydown = (event: KeyboardEvent) => {
		if (event.ctrlKey || event.metaKey) {
			switch (event.key) {
				case 'k':
					event.preventDefault();
					document.getElementById('search-input')?.focus();
					break;
				case 'f':
					event.preventDefault();
					showFilters = !showFilters;
					break;
			}
		}

		if (event.key === 'Escape') {
			if (showFilters) {
				showFilters = false;
			} else if (hasQuery || hasActiveFilters) {
				clearAllFilters();
			}
		}
	};

	// Add keyboard listener
	onMount(() => {
		document.addEventListener('keydown', handleKeydown);
		return () => document.removeEventListener('keydown', handleKeydown);
	});
</script>

<svelte:head>
	<title>搜尋筆記 - Life Note</title>
</svelte:head>

<div class="search-page h-full flex flex-col">
	<!-- Search Header -->
	<header class="flex-shrink-0 border-b border-border bg-background/95 backdrop-blur">
		<div class="container mx-auto px-4 py-4">
			<!-- Search Bar -->
			<div class="flex items-center space-x-4 mb-4">
				<div class="flex-1 relative">
					<Search
						class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
					/>
					<input
						id="search-input"
						type="text"
						value={searchQuery}
						oninput={handleSearchInput}
						placeholder="搜尋筆記標題、內容、標籤..."
						class="input pl-10 pr-4 py-3 text-lg"
						autocomplete="off"
					/>
					{#if hasQuery}
						<button
							onclick={clearAllFilters}
							class="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
						>
							<X class="h-4 w-4" />
						</button>
					{/if}
				</div>

				<Button
					variant={showFilters ? 'default' : 'outline'}
					size="lg"
					on:click={() => (showFilters = !showFilters)}
					class="relative"
				>
					<Filter class="h-4 w-4 mr-2" />
					篩選
					{#if hasActiveFilters}
						<span class="absolute -top-1 -right-1 h-3 w-3 bg-primary rounded-full"></span>
					{/if}
				</Button>
			</div>

			<!-- Search Stats and Controls -->
			{#if hasQuery || hasActiveFilters}
				<div class="flex items-center justify-between">
					<div class="flex items-center space-x-4 text-sm text-muted-foreground">
						{#if isSearching}
							<div class="flex items-center">
								<div class="spinner h-4 w-4 mr-2"></div>
								搜尋中...
							</div>
						{:else}
							<span>
								找到 {totalResults} 個結果
								{#if searchTime > 0}
									<span class="ml-2">({Math.round(searchTime)}ms)</span>
								{/if}
							</span>
						{/if}

						{#if hasActiveFilters}
							<button
								onclick={clearAllFilters}
								class="text-primary hover:text-primary/80 underline"
							>
								清除所有篩選
							</button>
						{/if}
					</div>

					<div class="flex items-center space-x-2">
						<!-- Sort Options -->
						<div class="flex items-center space-x-1">
							<Button
								variant={sortBy === 'relevance' ? 'secondary' : 'ghost'}
								size="sm"
								on:click={() => handleSortChange('relevance')}
							>
								相關性
							</Button>
							<Button
								variant={sortBy === 'date' ? 'secondary' : 'ghost'}
								size="sm"
								on:click={() => handleSortChange('date')}
								class="flex items-center"
							>
								<Calendar class="h-3 w-3 mr-1" />
								日期
								{#if sortBy === 'date'}
									{#if sortOrder === 'desc'}
										<SortDesc class="h-3 w-3 ml-1" />
									{:else}
										<SortAsc class="h-3 w-3 ml-1" />
									{/if}
								{/if}
							</Button>
							<Button
								variant={sortBy === 'title' ? 'secondary' : 'ghost'}
								size="sm"
								on:click={() => handleSortChange('title')}
							>
								標題
							</Button>
						</div>

						<div class="w-px h-6 bg-border"></div>

						<!-- View Mode Toggle -->
						<Button
							variant="ghost"
							size="sm"
							onclick={toggleViewMode}
							title={viewMode === 'grid' ? '切換到列表視圖' : '切換到網格視圖'}
						>
							{#if viewMode === 'grid'}
								<List class="h-4 w-4" />
							{:else}
								<Grid class="h-4 w-4" />
							{/if}
						</Button>
					</div>
				</div>
			{/if}
		</div>
	</header>

	<!-- Main Content -->
	<div class="flex-1 flex overflow-hidden">
		<!-- Filters Sidebar -->
		{#if showFilters}
			<aside class="w-80 border-r border-border bg-muted/30 overflow-y-auto">
				<SearchFilters
					{selectedCategories}
					{selectedTags}
					{selectedStatus}
					{selectedPriority}
					{dateRange}
					on:change={handleFiltersChange}
				/>
			</aside>
		{/if}

		<!-- Search Results -->
		<main class="flex-1 overflow-y-auto">
			{#if !hasQuery && !hasActiveFilters}
				<!-- Empty State -->
				<div class="flex flex-col items-center justify-center h-full text-center p-8">
					<Search class="h-16 w-16 text-muted-foreground mb-4" />
					<h2 class="text-2xl font-semibold mb-2">搜尋您的筆記</h2>
					<p class="text-muted-foreground mb-6 max-w-md">
						輸入關鍵字來搜尋筆記標題、內容或標籤。您也可以使用篩選器來縮小搜尋範圍。
					</p>

					<div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md">
						<Card class="p-4 text-left">
							<h3 class="font-medium mb-2">快捷鍵</h3>
							<ul class="text-sm text-muted-foreground space-y-1">
								<li><kbd class="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+K</kbd> 聚焦搜尋</li>
								<li><kbd class="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+F</kbd> 切換篩選</li>
								<li><kbd class="px-1 py-0.5 bg-muted rounded text-xs">Esc</kbd> 清除搜尋</li>
							</ul>
						</Card>

						<Card class="p-4 text-left">
							<h3 class="font-medium mb-2">搜尋技巧</h3>
							<ul class="text-sm text-muted-foreground space-y-1">
								<li>使用引號搜尋完整詞組</li>
								<li>用 - 排除特定詞彙</li>
								<li>結合篩選器精確搜尋</li>
							</ul>
						</Card>
					</div>
				</div>
			{:else}
				<SearchResults
					results={searchResults}
					{viewMode}
					{isSearching}
					query={searchQuery}
					onnote-click={e => handleNoteClick(e.detail.noteId)}
				/>
			{/if}
		</main>
	</div>
</div>

<style>
	.search-page {
		height: calc(100vh - 3.5rem); /* Subtract header height */
	}

	kbd {
		font-family: inherit;
		font-size: inherit;
	}
</style>
