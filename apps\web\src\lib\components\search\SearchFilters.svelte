<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { Icon } from '$components/ui';

	// Props
	interface Props {
		selectedCategories?: string[];
		selectedTags?: string[];
		selectedStatus?: string[];
		selectedPriority?: string[];
		dateRange?: { start: Date | null; end: Date | null };
	}

	let {
		selectedCategories = [],
		selectedTags = [],
		selectedStatus = [],
		selectedPriority = [],
		dateRange = { start: null, end: null }
	}: Props = $props();

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		change: {
			categories: string[];
			tags: string[];
			status: string[];
			priority: string[];
			dateRange: { start: Date | null; end: Date | null };
		};
	}>();

	// Filter sections state
	let sectionsExpanded = $state({
		categories: true,
		tags: true,
		status: true,
		priority: true,
		date: true
	});

	// Available options (these would come from API in real app)
	const availableCategories = [
		{ name: '工作', count: 25 },
		{ name: '學習', count: 18 },
		{ name: '個人', count: 12 },
		{ name: '項目', count: 8 },
		{ name: '想法', count: 15 }
	];

	const availableTags = [
		{ name: 'javascript', count: 20 },
		{ name: 'svelte', count: 15 },
		{ name: 'typescript', count: 18 },
		{ name: 'web-dev', count: 22 },
		{ name: 'tutorial', count: 10 },
		{ name: 'notes', count: 30 },
		{ name: 'ideas', count: 12 }
	];

	const statusOptions = [
		{ value: 'draft', label: '草稿', count: 15 },
		{ value: 'published', label: '已發布', count: 45 },
		{ value: 'archived', label: '已封存', count: 8 }
	];

	const priorityOptions = [
		{ value: 'low', label: '低', count: 20 },
		{ value: 'medium', label: '中', count: 35 },
		{ value: 'high', label: '高', count: 10 },
		{ value: 'urgent', label: '緊急', count: 3 }
	];

	// Toggle functions
	const toggleSection = (section: keyof typeof sectionsExpanded) => {
		sectionsExpanded[section] = !sectionsExpanded[section];
	};

	const toggleCategory = (category: string) => {
		if (selectedCategories.includes(category)) {
			selectedCategories = selectedCategories.filter(c => c !== category);
		} else {
			selectedCategories = [...selectedCategories, category];
		}
		emitChange();
	};

	const toggleTag = (tag: string) => {
		if (selectedTags.includes(tag)) {
			selectedTags = selectedTags.filter(t => t !== tag);
		} else {
			selectedTags = [...selectedTags, tag];
		}
		emitChange();
	};

	const toggleStatus = (status: string) => {
		if (selectedStatus.includes(status)) {
			selectedStatus = selectedStatus.filter(s => s !== status);
		} else {
			selectedStatus = [...selectedStatus, status];
		}
		emitChange();
	};

	const togglePriority = (priority: string) => {
		if (selectedPriority.includes(priority)) {
			selectedPriority = selectedPriority.filter(p => p !== priority);
		} else {
			selectedPriority = [...selectedPriority, priority];
		}
		emitChange();
	};

	// Date range handlers
	const handleDateRangeChange = () => {
		emitChange();
	};

	const handleStartDateChange = (event: Event) => {
		const target = event.target as HTMLInputElement;
		dateRange.start = parseDateFromInput(target.value);
		handleDateRangeChange();
	};

	const handleEndDateChange = (event: Event) => {
		const target = event.target as HTMLInputElement;
		dateRange.end = parseDateFromInput(target.value);
		handleDateRangeChange();
	};

	const clearDateRange = () => {
		dateRange = { start: null, end: null };
		emitChange();
	};

	// Emit change event
	const emitChange = () => {
		dispatch('change', {
			categories: selectedCategories,
			tags: selectedTags,
			status: selectedStatus,
			priority: selectedPriority,
			dateRange
		});
	};

	// Clear all filters
	const clearAllFilters = () => {
		selectedCategories = [];
		selectedTags = [];
		selectedStatus = [];
		selectedPriority = [];
		dateRange = { start: null, end: null };
		emitChange();
	};

	// Format date for input
	const formatDateForInput = (date: Date | null): string => {
		if (!date) return '';
		return date.toISOString().split('T')[0];
	};

	// Parse date from input
	const parseDateFromInput = (dateString: string): Date | null => {
		if (!dateString) return null;
		return new Date(dateString);
	};

	// Priority colors
	const priorityColors: Record<string, string> = {
		low: 'text-blue-600 bg-blue-50',
		medium: 'text-green-600 bg-green-50',
		high: 'text-orange-600 bg-orange-50',
		urgent: 'text-red-600 bg-red-50'
	};

	// Status colors
	const statusColors: Record<string, string> = {
		draft: 'text-yellow-600 bg-yellow-50',
		published: 'text-green-600 bg-green-50',
		archived: 'text-gray-600 bg-gray-50'
	};
</script>

<div class="search-filters p-4 space-y-4">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<h2 class="font-semibold">篩選器</h2>
		<Button variant="ghost" size="sm" on:click={clearAllFilters}>清除全部</Button>
	</div>

	<!-- Categories -->
	<Card class="p-3">
		<button
			class="w-full flex items-center justify-between text-left"
			onclick={() => toggleSection('categories')}
		>
			<div class="flex items-center">
				<Icon icon="folder-open" class="h-4 w-4 mr-2" />
				<span class="font-medium">分類</span>
				{#if selectedCategories.length > 0}
					<span class="ml-2 px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs">
						{selectedCategories.length}
					</span>
				{/if}
			</div>
			{#if sectionsExpanded.categories}
				<Icon icon="chevron-down" class="h-4 w-4" />
			{:else}
				<Icon icon="chevron-right" class="h-4 w-4" />
			{/if}
		</button>

		{#if sectionsExpanded.categories}
			<div class="mt-3 space-y-2">
				{#each availableCategories as category}
					<label
						class="flex items-center justify-between cursor-pointer hover:bg-muted/50 rounded p-1"
					>
						<div class="flex items-center">
							<input
								type="checkbox"
								checked={selectedCategories.includes(category.name)}
								onchange={() => toggleCategory(category.name)}
								class="mr-2 rounded border-input"
							/>
							<span class="text-sm">{category.name}</span>
						</div>
						<span class="text-xs text-muted-foreground">{category.count}</span>
					</label>
				{/each}
			</div>
		{/if}
	</Card>

	<!-- Tags -->
	<Card class="p-3">
		<button
			class="w-full flex items-center justify-between text-left"
			onclick={() => toggleSection('tags')}
		>
			<div class="flex items-center">
				<Icon icon="tag" class="h-4 w-4 mr-2" />
				<span class="font-medium">標籤</span>
				{#if selectedTags.length > 0}
					<span class="ml-2 px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs">
						{selectedTags.length}
					</span>
				{/if}
			</div>
			{#if sectionsExpanded.tags}
				<Icon icon="chevron-down" class="h-4 w-4" />
			{:else}
				<Icon icon="chevron-right" class="h-4 w-4" />
			{/if}
		</button>

		{#if sectionsExpanded.tags}
			<div class="mt-3 space-y-2">
				{#each availableTags as tag}
					<label
						class="flex items-center justify-between cursor-pointer hover:bg-muted/50 rounded p-1"
					>
						<div class="flex items-center">
							<input
								type="checkbox"
								checked={selectedTags.includes(tag.name)}
								onchange={() => toggleTag(tag.name)}
								class="mr-2 rounded border-input"
							/>
							<span class="text-sm font-mono">#{tag.name}</span>
						</div>
						<span class="text-xs text-muted-foreground">{tag.count}</span>
					</label>
				{/each}
			</div>
		{/if}
	</Card>

	<!-- Status -->
	<Card class="p-3">
		<button
			class="w-full flex items-center justify-between text-left"
			onclick={() => toggleSection('status')}
		>
			<div class="flex items-center">
				<Icon icon="file-text" class="h-4 w-4 mr-2" />
				<span class="font-medium">狀態</span>
				{#if selectedStatus.length > 0}
					<span class="ml-2 px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs">
						{selectedStatus.length}
					</span>
				{/if}
			</div>
			{#if sectionsExpanded.status}
				<Icon icon="chevron-down" class="h-4 w-4" />
			{:else}
				<Icon icon="chevron-right" class="h-4 w-4" />
			{/if}
		</button>

		{#if sectionsExpanded.status}
			<div class="mt-3 space-y-2">
				{#each statusOptions as status}
					<label
						class="flex items-center justify-between cursor-pointer hover:bg-muted/50 rounded p-1"
					>
						<div class="flex items-center">
							<input
								type="checkbox"
								checked={selectedStatus.includes(status.value)}
								onchange={() => toggleStatus(status.value)}
								class="mr-2 rounded border-input"
							/>
							<span class="text-sm px-2 py-1 rounded-full {statusColors[status.value]}">
								{status.label}
							</span>
						</div>
						<span class="text-xs text-muted-foreground">{status.count}</span>
					</label>
				{/each}
			</div>
		{/if}
	</Card>

	<!-- Priority -->
	<Card class="p-3">
		<button
			class="w-full flex items-center justify-between text-left"
			onclick={() => toggleSection('priority')}
		>
			<div class="flex items-center">
				<Icon icon="alert-circle" class="h-4 w-4 mr-2" />
				<span class="font-medium">優先級</span>
				{#if selectedPriority.length > 0}
					<span class="ml-2 px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs">
						{selectedPriority.length}
					</span>
				{/if}
			</div>
			{#if sectionsExpanded.priority}
				<Icon icon="chevron-down" class="h-4 w-4" />
			{:else}
				<Icon icon="chevron-right" class="h-4 w-4" />
			{/if}
		</button>

		{#if sectionsExpanded.priority}
			<div class="mt-3 space-y-2">
				{#each priorityOptions as priority}
					<label
						class="flex items-center justify-between cursor-pointer hover:bg-muted/50 rounded p-1"
					>
						<div class="flex items-center">
							<input
								type="checkbox"
								checked={selectedPriority.includes(priority.value)}
								onchange={() => togglePriority(priority.value)}
								class="mr-2 rounded border-input"
							/>
							<span class="text-sm px-2 py-1 rounded-full {priorityColors[priority.value]}">
								{priority.label}
							</span>
						</div>
						<span class="text-xs text-muted-foreground">{priority.count}</span>
					</label>
				{/each}
			</div>
		{/if}
	</Card>

	<!-- Date Range -->
	<Card class="p-3">
		<button
			class="w-full flex items-center justify-between text-left"
			onclick={() => toggleSection('date')}
		>
			<div class="flex items-center">
				<Icon icon="calendar" class="h-4 w-4 mr-2" />
				<span class="font-medium">日期範圍</span>
				{#if dateRange.start || dateRange.end}
					<span class="ml-2 px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs">
						已設定
					</span>
				{/if}
			</div>
			{#if sectionsExpanded.date}
				<Icon icon="chevron-down" class="h-4 w-4" />
			{:else}
				<Icon icon="chevron-right" class="h-4 w-4" />
			{/if}
		</button>

		{#if sectionsExpanded.date}
			<div class="mt-3 space-y-3">
				<div>
					<label class="block text-sm font-medium mb-1">開始日期</label>
					<input
						type="date"
						value={formatDateForInput(dateRange.start)}
						onchange={handleStartDateChange}
						class="input text-sm"
					/>
				</div>

				<div>
					<label class="block text-sm font-medium mb-1">結束日期</label>
					<input
						type="date"
						value={formatDateForInput(dateRange.end)}
						onchange={handleEndDateChange}
						class="input text-sm"
					/>
				</div>

				{#if dateRange.start || dateRange.end}
					<Button variant="ghost" size="sm" on:click={clearDateRange} class="w-full">
						<Icon icon="x" class="h-3 w-3 mr-1" />
						清除日期範圍
					</Button>
				{/if}
			</div>
		{/if}
	</Card>
</div>

<style>
	/* Custom scrollbar */
	.search-filters {
		scrollbar-width: thin;
		scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
	}

	.search-filters::-webkit-scrollbar {
		width: 6px;
	}

	.search-filters::-webkit-scrollbar-track {
		background: transparent;
	}

	.search-filters::-webkit-scrollbar-thumb {
		background: hsl(var(--muted-foreground) / 0.3);
		border-radius: 3px;
	}

	.search-filters::-webkit-scrollbar-thumb:hover {
		background: hsl(var(--muted-foreground) / 0.5);
	}
</style>
