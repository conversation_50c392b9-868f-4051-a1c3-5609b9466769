<script lang="ts">
	import { onMount } from 'svelte';
	import {
		Bot,
		Lightbulb,
		FileText,
		MessageSquare,
		Wand2,
		RefreshCw,
		CheckCircle,
		AlertCircle,
		Loader2,
		Send,
		Copy,
		ThumbsUp,
		ThumbsDown
	} from 'lucide-svelte';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { simpleAgentManager } from '$lib/services/agent/SimpleAgentManager';
	import { allNotes } from '$stores/notes';
	import type { Note } from '$lib/types/note';

	// Props
	export let note: Note | null = null;
	export let mode: 'analyze' | 'enhance' | 'chat' = 'analyze';

	// 狀態
	let isProcessing = false;
	let analysisResult: any = null;
	let enhancementSuggestions: any = null;
	let chatMessages: Array<{
		id: string;
		type: 'user' | 'assistant';
		content: string;
		timestamp: Date;
	}> = [];
	let chatInput = '';
	let error = '';

	// 分析功能
	const analyzeNote = async () => {
		if (!note || !simpleAgentManager.isReady()) {
			error = 'Agent 未就緒或沒有選擇筆記';
			return;
		}

		isProcessing = true;
		error = '';

		try {
			const request = {
				id: `analyze-${Date.now()}`,
				type: 'note_analysis' as const,
				priority: 'normal' as const,
				input: {
					content: note.content,
					title: note.title,
					tags: note.tags || []
				},
				createdAt: new Date()
			};

			const result = await simpleAgentManager.executeTask(request);

			if (result.status === 'completed') {
				analysisResult = result.output;
				console.log('Note analysis completed:', analysisResult);
			} else {
				throw new Error(result.error || '分析失敗');
			}
		} catch (err) {
			console.error('Note analysis failed:', err);
			error = err instanceof Error ? err.message : '分析失敗';
		} finally {
			isProcessing = false;
		}
	};

	// 增強建議
	const generateEnhancements = async () => {
		if (!note || !simpleAgentManager.isReady()) {
			error = 'Agent 未就緒或沒有選擇筆記';
			return;
		}

		isProcessing = true;
		error = '';

		try {
			const request = {
				id: `enhance-${Date.now()}`,
				type: 'content_generation' as const,
				priority: 'normal' as const,
				input: {
					prompt: `請為以下筆記提供改進建議和增強內容：

標題：${note.title}
內容：${note.content}

請提供：
1. 結構改進建議
2. 內容補充建議
3. 相關主題推薦
4. 標籤建議`,
					style: 'professional',
					maxLength: 1000
				},
				createdAt: new Date()
			};

			const result = await simpleAgentManager.executeTask(request);

			if (result.status === 'completed') {
				enhancementSuggestions = result.output;
				console.log('Enhancement suggestions generated:', enhancementSuggestions);
			} else {
				throw new Error(result.error || '生成建議失敗');
			}
		} catch (err) {
			console.error('Enhancement generation failed:', err);
			error = err instanceof Error ? err.message : '生成建議失敗';
		} finally {
			isProcessing = false;
		}
	};

	// 聊天功能
	const sendChatMessage = async () => {
		if (!chatInput.trim() || !simpleAgentManager.isReady()) {
			return;
		}

		const userMessage = {
			id: `msg-${Date.now()}`,
			type: 'user' as const,
			content: chatInput.trim(),
			timestamp: new Date()
		};

		chatMessages = [...chatMessages, userMessage];
		const currentInput = chatInput;
		chatInput = '';
		isProcessing = true;

		try {
			// 構建上下文
			let context = '';
			if (note) {
				context = `當前筆記：
標題：${note.title}
內容：${note.content}
`;
			}

			const request = {
				id: `chat-${Date.now()}`,
				type: 'question_answering' as const,
				priority: 'normal' as const,
				input: {
					question: currentInput,
					context,
					language: 'zh-TW'
				},
				createdAt: new Date()
			};

			const result = await simpleAgentManager.executeTask(request);

			if (result.status === 'completed') {
				const assistantMessage = {
					id: `msg-${Date.now()}`,
					type: 'assistant' as const,
					content: result.output.answer || result.output,
					timestamp: new Date()
				};
				chatMessages = [...chatMessages, assistantMessage];
			} else {
				throw new Error(result.error || '回應失敗');
			}
		} catch (err) {
			console.error('Chat failed:', err);
			const errorMessage = {
				id: `msg-${Date.now()}`,
				type: 'assistant' as const,
				content: `抱歉，我遇到了問題：${err instanceof Error ? err.message : '未知錯誤'}`,
				timestamp: new Date()
			};
			chatMessages = [...chatMessages, errorMessage];
		} finally {
			isProcessing = false;
		}
	};

	// 複製內容
	const copyToClipboard = async (text: string) => {
		try {
			await navigator.clipboard.writeText(text);
			// 可以添加成功提示
		} catch (err) {
			console.error('Failed to copy:', err);
		}
	};

	// 清除聊天記錄
	const clearChat = () => {
		chatMessages = [];
	};

	// 鍵盤事件處理
	const handleKeyPress = (event: KeyboardEvent) => {
		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			sendChatMessage();
		}
	};

	$: isAgentReady = simpleAgentManager.isReady();
</script>

<Card class="p-6">
	<div class="flex items-center space-x-3 mb-6">
		<Bot class="h-6 w-6 text-primary" />
		<div>
			<h3 class="text-lg font-semibold">筆記 AI 助手</h3>
			<p class="text-sm text-muted-foreground">
				{#if note}
					正在協助：{note.title}
				{:else}
					請選擇一個筆記開始使用 AI 助手
				{/if}
			</p>
		</div>
	</div>

	{#if !isAgentReady}
		<div class="text-center py-8">
			<AlertCircle class="h-12 w-12 text-muted-foreground mx-auto mb-3" />
			<h4 class="font-medium mb-2">AI Agent 未就緒</h4>
			<p class="text-sm text-muted-foreground">請先在設置中配置 Gemini API Key</p>
		</div>
	{:else if !note}
		<div class="text-center py-8">
			<FileText class="h-12 w-12 text-muted-foreground mx-auto mb-3" />
			<h4 class="font-medium mb-2">請選擇筆記</h4>
			<p class="text-sm text-muted-foreground">選擇一個筆記來使用 AI 助手功能</p>
		</div>
	{:else}
		<!-- 功能標籤頁 -->
		<div class="border-b border-border mb-6">
			<nav class="flex space-x-6">
				<button
					class="py-2 px-1 border-b-2 font-medium text-sm {mode === 'analyze'
						? 'border-primary text-primary'
						: 'border-transparent text-muted-foreground hover:text-foreground'}"
					onclick={() => (mode = 'analyze')}
				>
					<Lightbulb class="h-4 w-4 inline mr-2" />
					分析
				</button>

				<button
					class="py-2 px-1 border-b-2 font-medium text-sm {mode === 'enhance'
						? 'border-primary text-primary'
						: 'border-transparent text-muted-foreground hover:text-foreground'}"
					onclick={() => (mode = 'enhance')}
				>
					<Wand2 class="h-4 w-4 inline mr-2" />
					增強
				</button>

				<button
					class="py-2 px-1 border-b-2 font-medium text-sm {mode === 'chat'
						? 'border-primary text-primary'
						: 'border-transparent text-muted-foreground hover:text-foreground'}"
					onclick={() => (mode = 'chat')}
				>
					<MessageSquare class="h-4 w-4 inline mr-2" />
					對話
				</button>
			</nav>
		</div>

		<!-- 錯誤顯示 -->
		{#if error}
			<div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
				<div class="flex items-center space-x-2">
					<AlertCircle class="h-4 w-4 text-red-600" />
					<span class="text-sm text-red-800">{error}</span>
				</div>
			</div>
		{/if}

		<!-- 分析模式 -->
		{#if mode === 'analyze'}
			<div class="space-y-4">
				<div class="flex items-center justify-between">
					<h4 class="font-medium">筆記分析</h4>
					<Button variant="outline" size="sm" onclick={analyzeNote} disabled={isProcessing}>
						{#if isProcessing}
							<Loader2 class="h-4 w-4 mr-2 animate-spin" />
							分析中...
						{:else}
							<Lightbulb class="h-4 w-4 mr-2" />
							開始分析
						{/if}
					</Button>
				</div>

				{#if analysisResult}
					<div class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div class="p-4 bg-blue-50 rounded-lg">
								<h5 class="font-medium text-blue-900 mb-2">情感分析</h5>
								<p class="text-sm text-blue-800 capitalize">{analysisResult.sentiment || '中性'}</p>
							</div>

							<div class="p-4 bg-green-50 rounded-lg">
								<h5 class="font-medium text-green-900 mb-2">複雜度</h5>
								<p class="text-sm text-green-800 capitalize">
									{analysisResult.complexity || '中等'}
								</p>
							</div>
						</div>

						{#if analysisResult.topics && analysisResult.topics.length > 0}
							<div class="p-4 bg-purple-50 rounded-lg">
								<h5 class="font-medium text-purple-900 mb-2">主要主題</h5>
								<div class="flex flex-wrap gap-2">
									{#each analysisResult.topics as topic}
										<span class="px-2 py-1 text-xs bg-purple-200 text-purple-800 rounded"
											>{topic}</span
										>
									{/each}
								</div>
							</div>
						{/if}

						{#if analysisResult.keyPoints && analysisResult.keyPoints.length > 0}
							<div class="p-4 bg-yellow-50 rounded-lg">
								<h5 class="font-medium text-yellow-900 mb-2">關鍵要點</h5>
								<ul class="text-sm text-yellow-800 space-y-1">
									{#each analysisResult.keyPoints as point}
										<li>• {point}</li>
									{/each}
								</ul>
							</div>
						{/if}

						{#if analysisResult.suggestions && analysisResult.suggestions.length > 0}
							<div class="p-4 bg-orange-50 rounded-lg">
								<h5 class="font-medium text-orange-900 mb-2">改進建議</h5>
								<ul class="text-sm text-orange-800 space-y-1">
									{#each analysisResult.suggestions as suggestion}
										<li>• {suggestion}</li>
									{/each}
								</ul>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		{:else if mode === 'enhance'}
			<!-- 增強模式 -->
			<div class="space-y-4">
				<div class="flex items-center justify-between">
					<h4 class="font-medium">內容增強</h4>
					<Button
						variant="outline"
						size="sm"
						onclick={generateEnhancements}
						disabled={isProcessing}
					>
						{#if isProcessing}
							<Loader2 class="h-4 w-4 mr-2 animate-spin" />
							生成中...
						{:else}
							<Wand2 class="h-4 w-4 mr-2" />
							生成建議
						{/if}
					</Button>
				</div>

				{#if enhancementSuggestions}
					<div class="p-4 bg-muted/30 rounded-lg">
						<div class="flex items-center justify-between mb-3">
							<h5 class="font-medium">AI 增強建議</h5>
							<Button
								variant="ghost"
								size="sm"
								on:click={() =>
									copyToClipboard(enhancementSuggestions.content || enhancementSuggestions)}
							>
								<Copy class="h-4 w-4" />
							</Button>
						</div>
						<div class="text-sm whitespace-pre-wrap">
							{enhancementSuggestions.content || enhancementSuggestions}
						</div>
					</div>
				{/if}
			</div>
		{:else if mode === 'chat'}
			<!-- 對話模式 -->
			<div class="space-y-4">
				<div class="flex items-center justify-between">
					<h4 class="font-medium">AI 對話</h4>
					{#if chatMessages.length > 0}
						<Button variant="outline" size="sm" on:click={clearChat}>清除對話</Button>
					{/if}
				</div>

				<!-- 聊天記錄 -->
				<div class="h-64 overflow-y-auto border border-border rounded-lg p-4 space-y-3">
					{#if chatMessages.length === 0}
						<div class="text-center text-muted-foreground">
							<MessageSquare class="h-8 w-8 mx-auto mb-2" />
							<p class="text-sm">開始與 AI 對話，詢問關於這個筆記的任何問題</p>
						</div>
					{:else}
						{#each chatMessages as message}
							<div class="flex {message.type === 'user' ? 'justify-end' : 'justify-start'}">
								<div
									class="max-w-xs lg:max-w-md px-3 py-2 rounded-lg {message.type === 'user'
										? 'bg-primary text-primary-foreground'
										: 'bg-muted'}"
								>
									<p class="text-sm whitespace-pre-wrap">{message.content}</p>
									<p class="text-xs opacity-70 mt-1">
										{message.timestamp.toLocaleTimeString()}
									</p>
								</div>
							</div>
						{/each}
					{/if}
				</div>

				<!-- 輸入框 -->
				<div class="flex space-x-2">
					<input
						type="text"
						bind:value={chatInput}
						placeholder="輸入您的問題..."
						class="input flex-1"
						disabled={isProcessing}
						onkeypress={handleKeyPress}
					/>
					<Button onclick={sendChatMessage} disabled={isProcessing || !chatInput.trim()}>
						{#if isProcessing}
							<Loader2 class="h-4 w-4 animate-spin" />
						{:else}
							<Send class="h-4 w-4" />
						{/if}
					</Button>
				</div>
			</div>
		{/if}
	{/if}
</Card>
