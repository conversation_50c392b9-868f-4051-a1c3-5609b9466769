<script lang="ts">
	import { onMount } from 'svelte';
	import { Plus, Settings } from 'lucide-svelte';
	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { browser } from '$app/environment';

	// 簡單的狀態變數
	let testMessage = '點擊按鈕測試功能';
	let counter = 0;
	let showForm = false;

	// 測試函數
	function testClick() {
		console.log('testClick function called');
		counter++;
		testMessage = `按鈕已點擊 ${counter} 次`;
		console.log('Test button clicked:', counter);
	}

	function toggleForm() {
		console.log('toggleForm function called');
		showForm = !showForm;
		console.log('Toggle form:', showForm);
	}

	function resetTest() {
		console.log('resetTest function called');
		counter = 0;
		testMessage = '計數器已重置';
		showForm = false;
		console.log('Reset test');
	}

	onMount(() => {
		console.log('MCPTestConfig mounted');

		// 檢查 DOM 元素
		setTimeout(() => {
			const buttons = document.querySelectorAll('button');
			console.log('Found buttons:', buttons.length);
			buttons.forEach((btn, index) => {
				console.log(`Button ${index}:`, btn.textContent, btn.disabled, btn.style.pointerEvents);
			});
		}, 1000);
	});
</script>

<div class="space-y-6">
	<!-- 標題 -->
	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-xl font-semibold">MCP 配置測試</h2>
			<p class="text-sm text-muted-foreground">測試按鈕點擊功能</p>
		</div>
	</div>

	<!-- 測試區域 -->
	<Card class="p-6">
		<h3 class="text-lg font-medium mb-4">按鈕測試</h3>

		<div class="space-y-4">
			<p class="text-sm">{testMessage}</p>

			<!-- 原生按鈕測試 -->
			<div class="space-y-2">
				<h4 class="text-sm font-medium">原生按鈕測試</h4>
				<div class="flex space-x-2">
					<button
						class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
						onclick={testClick}
					>
						原生按鈕 ({counter})
					</button>

					<button
						class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
						onclick={toggleForm}
					>
						切換表單
					</button>
				</div>
			</div>

			<!-- UI 組件按鈕測試 -->
			<div class="space-y-2">
				<h4 class="text-sm font-medium">UI 組件按鈕測試</h4>
				<div class="flex space-x-2">
					<Button onclick={testClick} style="z-index: 1000; position: relative;">
						<Plus class="h-4 w-4 mr-2" />
						測試點擊 ({counter})
					</Button>

					<Button variant="outline" onclick={toggleForm} style="z-index: 1000; position: relative;">
						<Settings class="h-4 w-4 mr-2" />
						切換表單
					</Button>

					<Button
						variant="destructive"
						onclick={resetTest}
						style="z-index: 1000; position: relative;"
					>
						重置
					</Button>
				</div>
			</div>
		</div>
	</Card>

	<!-- 條件顯示測試 -->
	{#if showForm}
		<Card class="p-6">
			<h3 class="text-lg font-medium mb-4">表單測試</h3>

			<div class="space-y-4">
				<p class="text-sm text-green-600">✅ 表單顯示成功！</p>

				<div>
					<label class="block text-sm font-medium mb-2">測試輸入</label>
					<input
						type="text"
						placeholder="輸入測試文字"
						class="input"
					/>
				</div>

				<div class="flex justify-end space-x-2">
					<Button variant="outline" onclick={() => (showForm = false)}>關閉</Button>
					<Button onclick={() => alert('表單提交測試')}>提交</Button>
				</div>
			</div>
		</Card>
	{/if}

	<!-- 調試信息 -->
	<Card class="p-6">
		<h3 class="text-lg font-medium mb-4">調試信息</h3>

		<div class="space-y-2 text-sm">
			<p><strong>Browser:</strong> {browser ? '✅ 可用' : '❌ 不可用'}</p>
			<p><strong>Counter:</strong> {counter}</p>
			<p><strong>Show Form:</strong> {showForm ? '✅ 顯示' : '❌ 隱藏'}</p>
			<p><strong>Current Time:</strong> {new Date().toLocaleTimeString()}</p>
		</div>

		<div class="mt-4">
			<p class="text-xs text-muted-foreground">如果按鈕無法點擊，請檢查瀏覽器控制台的錯誤信息。</p>
		</div>
	</Card>
</div>
