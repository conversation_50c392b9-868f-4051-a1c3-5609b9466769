<script lang="ts">
	import { tv } from 'tailwind-variants';
	import type { HTMLAttributes } from 'svelte/elements';

	const cardVariants = tv({
		base: 'rounded-lg border bg-card text-card-foreground shadow-sm',
		variants: {
			variant: {
				default: '',
				outline: 'border-2',
				ghost: 'border-transparent shadow-none'
			},
			padding: {
				none: '',
				sm: 'p-4',
				default: 'p-6',
				lg: 'p-8'
			}
		},
		defaultVariants: {
			variant: 'default',
			padding: 'none'
		}
	});

	interface Props extends HTMLAttributes<HTMLDivElement> {
		variant?: 'default' | 'outline' | 'ghost';
		padding?: 'none' | 'sm' | 'default' | 'lg';
		class?: string;
		children?: any;
	}

	const { variant = 'default', padding = 'none', class: className = '', children, ...restProps }: Props = $props();

	const computedClass = $derived(cardVariants({ variant, padding, class: className }));
</script>

<div
	class={computedClass}
	role="button"
	tabindex="0"
	{...restProps}
>
	{@render children?.()}
</div>
