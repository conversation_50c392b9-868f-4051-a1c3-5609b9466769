<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Button, Card, Icon } from '$components/ui';
	import type { SearchResult } from '$lib/services/searchService';

	export let result: SearchResult;
	export let showScore = false;

	const dispatch = createEventDispatcher<{
		view: { note: typeof result.note };
		edit: { note: typeof result.note };
		select: { note: typeof result.note };
	}>();

	function formatDate(date: Date | string) {
		const d = typeof date === 'string' ? new Date(date) : date;
		return new Intl.DateTimeFormat('zh-TW', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		}).format(d);
	}

	function getStatusColor(status: string) {
		switch (status) {
			case 'draft':
				return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
			case 'published':
				return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
			case 'archived':
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
		}
	}

	function getPriorityColor(priority: string) {
		switch (priority) {
			case 'urgent':
				return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
			case 'high':
				return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
			case 'medium':
				return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
			case 'low':
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
		}
	}

	function getPriorityLabel(priority: string) {
		switch (priority) {
			case 'urgent':
				return '緊急';
			case 'high':
				return '高';
			case 'medium':
				return '中';
			case 'low':
				return '低';
			default:
				return priority;
		}
	}

	function getStatusLabel(status: string) {
		switch (status) {
			case 'draft':
				return '草稿';
			case 'published':
				return '已發布';
			case 'archived':
				return '已歸檔';
			default:
				return status;
		}
	}
</script>

<Card
	class="p-4 hover:shadow-md transition-shadow cursor-pointer"
	onclick={() => dispatch('select', { note: result.note })}
>
	<div class="space-y-3">
		<!-- 標題和分數 -->
		<div class="flex items-start justify-between">
			<div class="flex-1 min-w-0">
				<h3 class="text-lg font-semibold truncate">
					{#if result.highlights.title}
						{@html result.highlights.title}
					{:else}
						{result.note.title}
					{/if}
				</h3>
			</div>
			{#if showScore}
				<div class="flex items-center gap-1 ml-2">
					<Icon icon="star" class="h-4 w-4 text-yellow-500" />
					<span class="text-sm text-muted-foreground">{result.score.toFixed(1)}</span>
				</div>
			{/if}
		</div>

		<!-- 狀態和優先級標籤 -->
		<div class="flex items-center gap-2">
			<span
				class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {getStatusColor(
					result.note.status
				)}"
			>
				{getStatusLabel(result.note.status)}
			</span>
			<span
				class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {getPriorityColor(
					result.note.priority
				)}"
			>
				{getPriorityLabel(result.note.priority)}
			</span>
		</div>

		<!-- 內容摘要 -->
		{#if result.note.excerpt}
			<p class="text-sm text-muted-foreground line-clamp-2">
				{#if result.highlights.excerpt}
					{@html result.highlights.excerpt}
				{:else}
					{result.note.excerpt}
				{/if}
			</p>
		{/if}

		<!-- 標籤 -->
		{#if result.note.tags && result.note.tags.length > 0}
			<div class="flex flex-wrap gap-1">
				{#each result.note.tags.slice(0, 5) as tag}
					<span
						class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary text-secondary-foreground {result.matchedTags.some(
							t => t.name === tag.name
						)
							? 'ring-2 ring-primary'
							: ''}"
					>
						<Icon icon="tag" class="h-3 w-3 mr-1" />
						{tag.name}
					</span>
				{/each}
				{#if result.note.tags.length > 5}
					<span
						class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-muted text-muted-foreground"
					>
						+{result.note.tags.length - 5}
					</span>
				{/if}
			</div>
		{/if}

		<!-- 元數據和操作 -->
		<div class="flex items-center justify-between">
			<div class="flex items-center gap-4 text-xs text-muted-foreground">
				<div class="flex items-center gap-1">
					<Icon icon="calendar" class="h-3 w-3" />
					{formatDate(result.note.updatedAt)}
				</div>
				{#if result.note.authorId}
					<div class="flex items-center gap-1">
						<Icon icon="file-text" class="h-3 w-3" />
						{result.note.authorId}
					</div>
				{/if}
			</div>

			<div class="flex items-center gap-1">
				<button
					class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 px-3"
					onclick={(e) => {
						e.stopPropagation();
						dispatch('view', { note: result.note });
					}}
				>
					<Icon icon="eye" class="h-4 w-4" />
				</button>
				<button
					class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 px-3"
					onclick={(e) => {
						e.stopPropagation();
						dispatch('edit', { note: result.note });
					}}
				>
					<Icon icon="edit" class="h-4 w-4" />
				</button>
			</div>
		</div>

		<!-- 高亮內容片段 -->
		{#if result.highlights.content}
			<div class="mt-3 p-2 bg-muted/50 rounded text-sm">
				<div class="text-xs text-muted-foreground mb-1">內容匹配：</div>
				<div class="line-clamp-2">
					{@html result.highlights.content}
				</div>
			</div>
		{/if}
	</div>
</Card>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	:global(mark) {
		background-color: hsl(var(--primary) / 0.2);
		color: hsl(var(--primary-foreground));
		padding: 0.125rem 0.25rem;
		border-radius: 0.25rem;
		font-weight: 500;
	}
</style>
