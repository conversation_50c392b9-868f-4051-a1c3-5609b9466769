// Icon mapping from lucide-svelte to @iconify/svelte
// This provides a centralized mapping for icon migration

export const iconMap = {
	// Navigation & Layout
	'Menu': 'lucide:menu',
	'Search': 'lucide:search',
	'Bell': 'lucide:bell',
	'Settings': 'lucide:settings',
	'User': 'lucide:user',
	'Moon': 'lucide:moon',
	'Sun': 'lucide:sun',
	'Plus': 'lucide:plus',
	'X': 'lucide:x',
	'ChevronDown': 'lucide:chevron-down',
	'ChevronUp': 'lucide:chevron-up',
	'ChevronLeft': 'lucide:chevron-left',
	'ChevronRight': 'lucide:chevron-right',
	'MoreHorizontal': 'lucide:more-horizontal',
	'MoreVertical': 'lucide:more-vertical',
	
	// Files & Documents
	'FileText': 'lucide:file-text',
	'File': 'lucide:file',
	'Folder': 'lucide:folder',
	'FolderOpen': 'lucide:folder-open',
	'Archive': 'lucide:archive',
	'Download': 'lucide:download',
	'Copy': 'lucide:copy',
	
	// Actions
	'Save': 'lucide:save',
	'Edit': 'lucide:edit',
	'Edit3': 'lucide:edit-3',
	'Trash2': 'lucide:trash-2',
	'Share': 'lucide:share',
	'Share2': 'lucide:share-2',
	'RefreshCw': 'lucide:refresh-cw',
	'RotateCcw': 'lucide:rotate-ccw',
	'ArrowLeft': 'lucide:arrow-left',
	'ArrowRight': 'lucide:arrow-right',
	'ExternalLink': 'lucide:external-link',
	
	// Status & Feedback
	'CheckCircle': 'lucide:check-circle',
	'AlertCircle': 'lucide:alert-circle',
	'AlertTriangle': 'lucide:alert-triangle',
	'Info': 'lucide:info',
	'Loader2': 'lucide:loader-2',
	'Check': 'lucide:check',
	'Star': 'lucide:star',
	'Heart': 'lucide:heart',
	
	// Media & Controls
	'Play': 'lucide:play',
	'Square': 'lucide:square',
	'Eye': 'lucide:eye',
	'EyeOff': 'lucide:eye-off',
	'Send': 'lucide:send',
	
	// Data & Analytics
	'BarChart3': 'lucide:bar-chart-3',
	'TrendingUp': 'lucide:trending-up',
	'Activity': 'lucide:activity',
	'Target': 'lucide:target',
	'Grid': 'lucide:grid-3x3',
	'List': 'lucide:list',
	
	// Tags & Categories
	'Tag': 'lucide:tag',
	'Hash': 'lucide:hash',
	
	// Time & Calendar
	'Clock': 'lucide:clock',
	'Calendar': 'lucide:calendar',
	'History': 'lucide:history',
	
	// Sorting & Filtering
	'Filter': 'lucide:filter',
	'SortAsc': 'lucide:arrow-up-a-z',
	'SortDesc': 'lucide:arrow-down-z-a',
	
	// AI & Bot
	'Bot': 'lucide:bot',
	'Sparkles': 'lucide:sparkles',
	'MessageSquare': 'lucide:message-square',
	'Wand2': 'lucide:wand-2',
	'Lightbulb': 'lucide:lightbulb',
	
	// Network & Connectivity
	'Wifi': 'lucide:wifi',
	'WifiOff': 'lucide:wifi-off',
	'Link': 'lucide:link',
	'Unlink': 'lucide:unlink',
	
	// Development & Code
	'GitBranch': 'lucide:git-branch',
	'Github': 'lucide:github',
	'Zap': 'lucide:zap',
	
	// UI Elements
	'Layers': 'lucide:layers',
	'ZoomIn': 'lucide:zoom-in',
	'ZoomOut': 'lucide:zoom-out',
	
	// Actions & Tools
	'Merge': 'lucide:merge',
	'Split': 'lucide:split',
	'Shield': 'lucide:shield',
	'ThumbsUp': 'lucide:thumbs-up',
	'ThumbsDown': 'lucide:thumbs-down',
	'Users': 'lucide:users',
	'LogOut': 'lucide:log-out',
	
	// Home & Navigation
	'Home': 'lucide:home',
	
	// Additional icons found in codebase
	'Languages': 'lucide:languages'
} as const;

export type LucideIconName = keyof typeof iconMap;
export type IconifyIconName = typeof iconMap[LucideIconName];
