<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import {
		Plus,
		Trash2,
		Edit3,
		Save,
		X,
		Copy,
		Download,
		Upload,
		Settings,
		Server,
		Terminal,
		CheckCircle,
		AlertCircle,
		Play,
		Square
	} from 'lucide-svelte';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { browser } from '$app/environment';

	const dispatch = createEventDispatcher();

	// MCP 服務器配置類型
	interface MCPServerConfig {
		name: string;
		command: string;
		args: string[];
		description?: string;
		enabled: boolean;
		status?: 'running' | 'stopped' | 'error';
	}

	// 狀態變數
	let mcpServers: Record<string, MCPServerConfig> = {};
	let editingServer: string | null = null;
	let newServerName = '';
	let newServerCommand = '';
	let newServerArgs = '';
	let newServerDescription = '';
	let showAddForm = false;
	let configJson = '';
	let showJsonEditor = false;
	let error = '';
	let success = '';

	// 預設配置模板
	const defaultConfigs = {
		'context7': {
			name: 'Context7',
			command: 'npx',
			args: ['-y', '@upstash/context7-mcp'],
			description: 'Context7 MCP Server for enhanced context management',
			enabled: true
		},
		'serena': {
			name: 'Serena',
			command: 'uv',
			args: ['run', '--directory', 'C:\\Users\\<USER>\\Documents\\git\\serena', 'serena-mcp-server'],
			description: 'Serena MCP Server for AI agent capabilities',
			enabled: true
		},
		'life-note': {
			name: 'Life Note',
			command: 'node',
			args: ['apps/mcp-server/index.js'],
			description: 'Life Note MCP Server for note management',
			enabled: true
		}
	};

	onMount(() => {
		loadConfiguration();
	});

	// 載入配置
	function loadConfiguration() {
		if (!browser) return;
		
		try {
			const saved = localStorage.getItem('mcp-servers-config');
			if (saved) {
				mcpServers = JSON.parse(saved);
			} else {
				// 使用預設配置
				mcpServers = { ...defaultConfigs };
				saveConfiguration();
			}
			updateJsonEditor();
		} catch (err) {
			console.error('Failed to load MCP configuration:', err);
			error = '載入配置失敗';
		}
	}

	// 保存配置
	function saveConfiguration() {
		if (!browser) return;

		try {
			localStorage.setItem('mcp-servers-config', JSON.stringify(mcpServers));
			success = '配置已保存';
			setTimeout(() => success = '', 3000);

			// 發送配置變化事件
			dispatch('configChanged', mcpServers);
		} catch (err) {
			console.error('Failed to save MCP configuration:', err);
			error = '保存配置失敗';
		}
	}

	// 更新 JSON 編輯器
	function updateJsonEditor() {
		const config = {
			mcpServers: Object.fromEntries(
				Object.entries(mcpServers).map(([key, server]) => [
					key,
					{
						command: server.command,
						args: server.args
					}
				])
			)
		};
		configJson = JSON.stringify(config, null, 2);
	}

	// 添加新服務器
	function addServer() {
		if (!newServerName.trim() || !newServerCommand.trim()) {
			error = '請填寫服務器名稱和命令';
			return;
		}

		const key = newServerName.toLowerCase().replace(/\s+/g, '-');
		if (mcpServers[key]) {
			error = '服務器名稱已存在';
			return;
		}

		mcpServers[key] = {
			name: newServerName.trim(),
			command: newServerCommand.trim(),
			args: newServerArgs.trim() ? newServerArgs.trim().split(' ') : [],
			description: newServerDescription.trim(),
			enabled: true,
			status: 'stopped'
		};

		// 重置表單
		newServerName = '';
		newServerCommand = '';
		newServerArgs = '';
		newServerDescription = '';
		showAddForm = false;

		saveConfiguration();
		updateJsonEditor();
	}

	// 刪除服務器
	function deleteServer(key: string) {
		if (confirm(`確定要刪除服務器 "${mcpServers[key].name}" 嗎？`)) {
			delete mcpServers[key];
			mcpServers = { ...mcpServers };
			saveConfiguration();
			updateJsonEditor();
		}
	}

	// 切換服務器啟用狀態
	function toggleServer(key: string) {
		mcpServers[key].enabled = !mcpServers[key].enabled;
		saveConfiguration();
		updateJsonEditor();
	}

	// 編輯服務器
	function startEdit(key: string) {
		editingServer = key;
	}

	function saveEdit(key: string) {
		editingServer = null;
		saveConfiguration();
		updateJsonEditor();
	}

	function cancelEdit() {
		editingServer = null;
		loadConfiguration(); // 重新載入以取消更改
	}

	// 從 JSON 導入配置
	function importFromJson() {
		try {
			const parsed = JSON.parse(configJson);
			if (parsed.mcpServers) {
				const imported: Record<string, MCPServerConfig> = {};
				
				Object.entries(parsed.mcpServers).forEach(([key, config]: [string, any]) => {
					imported[key] = {
						name: key.charAt(0).toUpperCase() + key.slice(1),
						command: config.command,
						args: config.args || [],
						description: config.description || '',
						enabled: true,
						status: 'stopped'
					};
				});
				
				mcpServers = imported;
				saveConfiguration();
				showJsonEditor = false;
				success = '配置已從 JSON 導入';
			} else {
				error = '無效的 JSON 格式';
			}
		} catch (err) {
			error = 'JSON 解析失敗';
		}
	}

	// 導出配置
	function exportConfig() {
		const blob = new Blob([configJson], { type: 'application/json' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = 'mcp-servers-config.json';
		a.click();
		URL.revokeObjectURL(url);
	}

	// 複製配置到剪貼板
	async function copyConfig() {
		try {
			await navigator.clipboard.writeText(configJson);
			success = '配置已複製到剪貼板';
		} catch (err) {
			error = '複製失敗';
		}
	}

	// 重置為預設配置
	function resetToDefault() {
		if (confirm('確定要重置為預設配置嗎？這將覆蓋所有現有配置。')) {
			mcpServers = { ...defaultConfigs };
			saveConfiguration();
			updateJsonEditor();
			success = '已重置為預設配置';
		}
	}

	// 載入配置文件
	function loadConfigFile(event: Event) {
		const input = event.target as HTMLInputElement;
		const file = input.files?.[0];
		if (!file) return;

		const reader = new FileReader();
		reader.onload = (e) => {
			try {
				const content = e.target?.result as string;
				configJson = content;
				importFromJson();
			} catch (err) {
				error = '文件讀取失敗';
			}
		};
		reader.readAsText(file);
	}

	$: {
		if (error) {
			setTimeout(() => error = '', 5000);
		}
	}
</script>

<div class="space-y-6">
	<!-- 標題和操作按鈕 -->
	<div class="flex items-center justify-between">
		<div class="flex items-center space-x-3">
			<Settings class="h-6 w-6 text-primary" />
			<div>
				<h2 class="text-xl font-semibold">MCP 服務器配置</h2>
				<p class="text-sm text-muted-foreground">管理 Model Context Protocol 服務器設置</p>
			</div>
		</div>
		
		<div class="flex items-center space-x-2">
			<Button variant="outline" size="sm" onclick={() => showJsonEditor = !showJsonEditor}>
				<Terminal class="h-4 w-4 mr-2" />
				JSON 編輯器
			</Button>

			<Button variant="outline" size="sm" onclick={() => showAddForm = !showAddForm}>
				<Plus class="h-4 w-4 mr-2" />
				添加服務器
			</Button>
		</div>
	</div>

	<!-- 錯誤和成功消息 -->
	{#if error}
		<div class="p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2">
			<AlertCircle class="h-4 w-4 text-red-600" />
			<span class="text-sm text-red-800">{error}</span>
		</div>
	{/if}

	{#if success}
		<div class="p-3 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-2">
			<CheckCircle class="h-4 w-4 text-green-600" />
			<span class="text-sm text-green-800">{success}</span>
		</div>
	{/if}

	<!-- 添加服務器表單 -->
	{#if showAddForm}
		<Card class="p-6">
			<h3 class="text-lg font-medium mb-4">添加新的 MCP 服務器</h3>
			
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<label class="block text-sm font-medium mb-2">服務器名稱</label>
					<input
						type="text"
						bind:value={newServerName}
						placeholder="例如: Context7"
						class="input"
					/>
				</div>
				
				<div>
					<label class="block text-sm font-medium mb-2">命令</label>
					<input
						type="text"
						bind:value={newServerCommand}
						placeholder="例如: npx"
						class="input"
					/>
				</div>
				
				<div class="md:col-span-2">
					<label class="block text-sm font-medium mb-2">參數 (用空格分隔)</label>
					<input
						type="text"
						bind:value={newServerArgs}
						placeholder="例如: -y @upstash/context7-mcp"
						class="input"
					/>
				</div>
				
				<div class="md:col-span-2">
					<label class="block text-sm font-medium mb-2">描述 (可選)</label>
					<input
						type="text"
						bind:value={newServerDescription}
						placeholder="服務器功能描述"
						class="input"
					/>
				</div>
			</div>
			
			<div class="flex justify-end space-x-2 mt-4">
				<Button variant="outline" onclick={() => showAddForm = false}>
					<X class="h-4 w-4 mr-2" />
					取消
				</Button>
				<Button onclick={addServer}>
					<Save class="h-4 w-4 mr-2" />
					添加
				</Button>
			</div>
		</Card>
	{/if}

	<!-- JSON 編輯器 -->
	{#if showJsonEditor}
		<Card class="p-6">
			<div class="flex items-center justify-between mb-4">
				<h3 class="text-lg font-medium">JSON 配置編輯器</h3>
				<div class="flex items-center space-x-2">
					<Button variant="outline" size="sm" onclick={copyConfig}>
						<Copy class="h-4 w-4 mr-2" />
						複製
					</Button>
					<Button variant="outline" size="sm" onclick={exportConfig}>
						<Download class="h-4 w-4 mr-2" />
						導出
					</Button>
					<label class="cursor-pointer">
						<Button variant="outline" size="sm">
							<Upload class="h-4 w-4 mr-2" />
							導入
						</Button>
						<input type="file" accept=".json" class="hidden" on:change={loadConfigFile} />
					</label>
				</div>
			</div>
			
			<textarea
				bind:value={configJson}
				rows="15"
				class="input font-mono text-sm"
				placeholder="JSON 配置..."
			></textarea>
			
			<div class="flex justify-end space-x-2 mt-4">
				<Button variant="outline" onclick={() => showJsonEditor = false}>
					取消
				</Button>
				<Button onclick={importFromJson}>
					<Save class="h-4 w-4 mr-2" />
					應用配置
				</Button>
			</div>
		</Card>
	{/if}

	<!-- 服務器列表 -->
	<div class="space-y-4">
		{#each Object.entries(mcpServers) as [key, server]}
			<Card class="p-6">
				<div class="flex items-center justify-between">
					<div class="flex items-center space-x-4">
						<div class="flex items-center space-x-2">
							<Server class="h-5 w-5 text-primary" />
							{#if editingServer === key}
								<input
									type="text"
									bind:value={server.name}
									class="text-lg font-medium bg-transparent border-b border-input focus:outline-none focus:border-primary dark:text-white"
								/>
							{:else}
								<h3 class="text-lg font-medium">{server.name}</h3>
							{/if}
						</div>
						
						<div class="flex items-center space-x-2">
							<div class="w-2 h-2 rounded-full {server.enabled ? 'bg-green-500' : 'bg-gray-400'}"></div>
							<span class="text-sm text-muted-foreground">
								{server.enabled ? '已啟用' : '已禁用'}
							</span>
						</div>
					</div>
					
					<div class="flex items-center space-x-2">
						<Button
							variant="ghost"
							size="sm"
							onclick={() => toggleServer(key)}
							class={server.enabled ? 'text-orange-600 hover:text-orange-700' : 'text-green-600 hover:text-green-700'}
						>
							{#if server.enabled}
								<Square class="h-4 w-4" />
							{:else}
								<Play class="h-4 w-4" />
							{/if}
						</Button>

						{#if editingServer === key}
							<Button variant="ghost" size="sm" onclick={() => saveEdit(key)}>
								<Save class="h-4 w-4" />
							</Button>
							<Button variant="ghost" size="sm" onclick={cancelEdit}>
								<X class="h-4 w-4" />
							</Button>
						{:else}
							<Button variant="ghost" size="sm" onclick={() => startEdit(key)}>
								<Edit3 class="h-4 w-4" />
							</Button>
						{/if}

						<Button variant="ghost" size="sm" onclick={() => deleteServer(key)} class="text-red-600 hover:text-red-700">
							<Trash2 class="h-4 w-4" />
						</Button>
					</div>
				</div>
				
				<div class="mt-4 space-y-2">
					{#if editingServer === key}
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label class="block text-sm font-medium mb-1">命令</label>
								<input
									type="text"
									bind:value={server.command}
									class="input"
								/>
							</div>
							<div>
								<label class="block text-sm font-medium mb-1">參數</label>
								<input
									type="text"
									value={server.args.join(' ')}
									on:input={(e) => server.args = e.target.value.split(' ').filter(Boolean)}
									class="input"
								/>
							</div>
							<div class="md:col-span-2">
								<label class="block text-sm font-medium mb-1">描述</label>
								<input
									type="text"
									bind:value={server.description}
									class="input"
								/>
							</div>
						</div>
					{:else}
						<div class="text-sm text-muted-foreground">
							<p><strong>命令:</strong> {server.command}</p>
							<p><strong>參數:</strong> {server.args.join(' ')}</p>
							{#if server.description}
								<p><strong>描述:</strong> {server.description}</p>
							{/if}
						</div>
					{/if}
				</div>
			</Card>
		{/each}
	</div>

	<!-- 操作按鈕 -->
	<div class="flex justify-center space-x-4">
		<Button variant="outline" onclick={resetToDefault}>
			重置為預設配置
		</Button>
		<Button onclick={updateJsonEditor}>
			更新 JSON 配置
		</Button>
	</div>
</div>
