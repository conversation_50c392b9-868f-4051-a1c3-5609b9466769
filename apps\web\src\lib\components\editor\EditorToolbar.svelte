<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Button, Icon } from '$components/ui';

	// Props
	interface Props {
		previewMode?: 'split' | 'preview' | 'edit';
	}

	let { previewMode = 'edit' }: Props = $props();

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		action: { action: string; data?: any };
		'preview-mode': 'split' | 'preview' | 'edit';
	}>();

	// Toolbar groups
	const formatGroup = [
		{ action: 'bold', icon: 'bold', title: '粗體 (Ctrl+B)', shortcut: 'Ctrl+B' },
		{ action: 'italic', icon: 'italic', title: '斜體 (Ctrl+I)', shortcut: 'Ctrl+I' },
		{ action: 'strikethrough', icon: 'strikethrough', title: '刪除線', shortcut: '' }
	];

	const insertGroup = [
		{ action: 'link', icon: 'link', title: '連結 (Ctrl+K)', shortcut: 'Ctrl+K' },
		{ action: 'image', icon: 'image', title: '圖片', shortcut: '' },
		{ action: 'code', icon: 'code', title: '行內代碼', shortcut: '' },
		{ action: 'codeblock', icon: 'code-2', title: '代碼塊', shortcut: '' }
	];

	const structureGroup = [
		{ action: 'heading', icon: 'heading-1', title: '標題 1', shortcut: '', data: { level: 1 } },
		{ action: 'heading', icon: 'heading-2', title: '標題 2', shortcut: '', data: { level: 2 } },
		{ action: 'heading', icon: 'heading-3', title: '標題 3', shortcut: '', data: { level: 3 } },
		{ action: 'quote', icon: 'quote', title: '引用', shortcut: '' },
		{ action: 'list', icon: 'list', title: '無序列表', shortcut: '' },
		{ action: 'orderedlist', icon: 'list-ordered', title: '有序列表', shortcut: '' },
		{ action: 'hr', icon: 'minus', title: '分隔線', shortcut: '' },
		{ action: 'table', icon: 'table', title: '表格', shortcut: '' }
	];

	// Handle action
	const handleAction = (action: string, data?: any) => {
		dispatch('action', { action, data });
	};

	// Handle preview mode toggle
	const togglePreviewMode = () => {
		let newMode: 'split' | 'preview' | 'edit';

		switch (previewMode) {
			case 'edit':
				newMode = 'split';
				break;
			case 'split':
				newMode = 'preview';
				break;
			case 'preview':
				newMode = 'edit';
				break;
			default:
				newMode = 'edit';
		}

		dispatch('preview-mode', newMode);
	};

	// Get preview button icon and title
	let previewIcon = $derived(previewMode === 'edit' ? 'eye' : previewMode === 'split' ? 'columns' : 'eye-off');
	let previewTitle = $derived(
		previewMode === 'edit' ? '顯示預覽' : previewMode === 'split' ? '僅預覽' : '隱藏預覽'
	);
</script>

<div class="editor-toolbar flex items-center gap-1 p-2 border-b border-border bg-muted/30">
	<!-- Format Group -->
	<div class="toolbar-group flex items-center gap-1">
		{#each formatGroup as item}
			<Button
				variant="ghost"
				size="sm"
				class="h-8 w-8 p-0"
				title={item.title}
				onclick={() => handleAction(item.action)}
			>
				<Icon icon={item.icon} class="h-4 w-4" />
			</Button>
		{/each}
	</div>

	<div class="separator w-px h-6 bg-border mx-1"></div>

	<!-- Insert Group -->
	<div class="toolbar-group flex items-center gap-1">
		{#each insertGroup as item}
			<Button
				variant="ghost"
				size="sm"
				class="h-8 w-8 p-0"
				title={item.title}
				onclick={() => handleAction(item.action)}
			>
				<Icon icon={item.icon} class="h-4 w-4" />
			</Button>
		{/each}
	</div>

	<div class="separator w-px h-6 bg-border mx-1"></div>

	<!-- Structure Group -->
	<div class="toolbar-group flex items-center gap-1">
		{#each structureGroup as item}
			<Button
				variant="ghost"
				size="sm"
				class="h-8 w-8 p-0"
				title={item.title}
				onclick={() => handleAction(item.action, item.data)}
			>
				<Icon icon={item.icon} class="h-4 w-4" />
			</Button>
		{/each}
	</div>

	<!-- Spacer -->
	<div class="flex-1"></div>

	<!-- Preview Toggle -->
	<div class="toolbar-group flex items-center gap-1">
		<Button
			variant="ghost"
			size="sm"
			class="h-8 w-8 p-0"
			title={previewTitle}
			onclick={togglePreviewMode}
		>
			<Icon icon={previewIcon} class="h-4 w-4" />
		</Button>
	</div>

	<div class="separator w-px h-6 bg-border mx-1"></div>

	<!-- Action Group -->
	<div class="toolbar-group flex items-center gap-1">
		<Button
			variant="ghost"
			size="sm"
			class="h-8 px-3"
			title="保存 (Ctrl+S)"
			onclick={() => handleAction('save')}
		>
			<Icon icon="save" class="h-4 w-4 mr-1" />
			保存
		</Button>
	</div>
</div>

<style>
	.editor-toolbar {
		user-select: none;
		flex-shrink: 0;
	}

	.toolbar-group {
		display: flex;
		align-items: center;
	}

	.separator {
		flex-shrink: 0;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.editor-toolbar {
			flex-wrap: wrap;
			gap: 0.25rem;
		}

		.toolbar-group {
			gap: 0.25rem;
		}

		.separator {
			display: none;
		}
	}
</style>
