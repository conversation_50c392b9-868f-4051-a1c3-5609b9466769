<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { <PERSON><PERSON>, Card, Icon } from '$components/ui';
	import MarkdownRenderer from '$lib/components/editor/MarkdownRenderer.svelte';
	import type { Note } from '$types';

	interface Props {
		note: Note;
	}

	let { note }: Props = $props();

	const dispatch = createEventDispatcher<{
		close: void;
		restore: { versionId: string };
		compare: { versionId1: string; versionId2: string };
	}>();

	// Mock 版本歷史數據
	const mockVersions = [
		{
			id: 'v1',
			version: '1.0.0',
			title: note.title,
			content: note.content,
			summary: '初始版本',
			authorId: 'user-1',
			authorName: '張三',
			createdAt: new Date(Date.now() - 86400000 * 7),
			changeType: 'create' as const,
			wordCount: note.content.length,
			isCurrent: true
		},
		{
			id: 'v2',
			version: '0.9.0',
			title: '歡迎使用 Life Note (草稿)',
			content: `# 歡迎使用 Life Note

Life Note 是一個知識管理系統。

## 主要功能

### 📝 Markdown 編輯
- 支援 Markdown 語法
- 實時預覽功能

### 🔗 依賴關係追蹤
- 檢測筆記間的關聯

## 開始使用

1. 創建您的第一個筆記
2. 使用 Markdown 語法編寫內容`,
			summary: '添加基本內容結構',
			authorId: 'user-1',
			authorName: '張三',
			createdAt: new Date(Date.now() - 86400000 * 8),
			changeType: 'update' as const,
			wordCount: 150,
			isCurrent: false
		},
		{
			id: 'v3',
			version: '0.5.0',
			title: '新筆記',
			content: `# 新筆記

這是一個新的筆記。`,
			summary: '創建筆記',
			authorId: 'user-1',
			authorName: '張三',
			createdAt: new Date(Date.now() - 86400000 * 10),
			changeType: 'create' as const,
			wordCount: 20,
			isCurrent: false
		}
	];

	let versions = $state(mockVersions);
	let selectedVersion = $state<(typeof versions)[0] | null>(null);
	let expandedVersions = $state(new Set<string>());
	let compareMode = $state(false);
	let compareVersions = $state<string[]>([]);

	onMount(() => {
		// 在真實應用中，這裡會從 API 加載版本歷史
		// versions = await noteStore.getVersionHistory(note.id);
	});

	function handleClose() {
		dispatch('close');
	}

	function handleRestore(versionId: string) {
		const confirmed = confirm('確定要恢復到此版本嗎？當前的更改將會丟失。');
		if (confirmed) {
			dispatch('restore', { versionId });
		}
	}

	function handlePreview(version: (typeof versions)[0]) {
		selectedVersion = selectedVersion?.id === version.id ? null : version;
	}

	function toggleExpanded(versionId: string) {
		if (expandedVersions.has(versionId)) {
			expandedVersions.delete(versionId);
		} else {
			expandedVersions.add(versionId);
		}
		expandedVersions = new Set(expandedVersions);
	}

	function toggleCompareMode() {
		compareMode = !compareMode;
		compareVersions = [];
	}

	function handleCompareSelect(versionId: string) {
		if (compareVersions.includes(versionId)) {
			compareVersions = compareVersions.filter(id => id !== versionId);
		} else if (compareVersions.length < 2) {
			compareVersions = [...compareVersions, versionId];
		}

		if (compareVersions.length === 2) {
			dispatch('compare', {
				versionId1: compareVersions[0],
				versionId2: compareVersions[1]
			});
		}
	}

	function formatDate(date: Date): string {
		return new Intl.DateTimeFormat('zh-TW', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		}).format(date);
	}

	function getChangeTypeColor(changeType: string): string {
		switch (changeType) {
			case 'create':
				return 'text-green-600 bg-green-100';
			case 'update':
				return 'text-blue-600 bg-blue-100';
			case 'delete':
				return 'text-red-600 bg-red-100';
			default:
				return 'text-gray-600 bg-gray-100';
		}
	}

	function getChangeTypeText(changeType: string): string {
		switch (changeType) {
			case 'create':
				return '創建';
			case 'update':
				return '更新';
			case 'delete':
				return '刪除';
			default:
				return '未知';
		}
	}

	function calculateWordDiff(current: number, previous: number): string {
		const diff = current - previous;
		if (diff > 0) return `+${diff}`;
		if (diff < 0) return `${diff}`;
		return '0';
	}
</script>

<Card class="version-history">
	<!-- 標題欄 -->
	<div class="header">
		<div class="flex items-center gap-2">
			<Icon icon="clock" class="h-5 w-5" />
			<h3 class="text-lg font-semibold">版本歷史</h3>
			<span class="text-sm text-muted-foreground">({versions.length} 個版本)</span>
		</div>

		<div class="flex items-center gap-2">
			<Button variant="outline" size="sm" onclick={toggleCompareMode}>
				<Icon icon="git-branch" class="h-4 w-4 mr-1" />
				{compareMode ? '取消比較' : '比較版本'}
			</Button>

			<Button variant="ghost" size="sm" onclick={handleClose}>
				<Icon icon="x" class="h-4 w-4" />
			</Button>
		</div>
	</div>

	<div class="content">
		<!-- 版本列表 -->
		<div class="version-list">
			{#each versions as version, index (version.id)}
				<div class="version-item" class:current={version.isCurrent}>
					<div class="version-header">
						<div class="flex items-center gap-2">
							<button
								type="button"
								class="expand-button"
								onclick={() => toggleExpanded(version.id)}
							>
								{#if expandedVersions.has(version.id)}
									<Icon icon="chevron-down" class="h-4 w-4" />
								{:else}
									<Icon icon="chevron-right" class="h-4 w-4" />
								{/if}
							</button>

							{#if compareMode}
								<input
									type="checkbox"
									checked={compareVersions.includes(version.id)}
									onchange={() => handleCompareSelect(version.id)}
									disabled={compareVersions.length >= 2 && !compareVersions.includes(version.id)}
								/>
							{/if}

							<div class="version-info">
								<div class="flex items-center gap-2">
									<span class="version-number">v{version.version}</span>
									<span class="change-type {getChangeTypeColor(version.changeType)}">
										{getChangeTypeText(version.changeType)}
									</span>
									{#if version.isCurrent}
										<span class="current-badge">當前版本</span>
									{/if}
								</div>

								<div class="version-meta">
									<div class="flex items-center gap-4 text-sm text-muted-foreground">
										<div class="flex items-center gap-1">
											<Icon icon="user" class="h-3 w-3" />
											<span>{version.authorName}</span>
										</div>
										<div class="flex items-center gap-1">
											<Icon icon="calendar" class="h-3 w-3" />
											<span>{formatDate(version.createdAt)}</span>
										</div>
										<div class="flex items-center gap-1">
											<Icon icon="file-text" class="h-3 w-3" />
											<span>{version.wordCount} 字符</span>
											{#if index < versions.length - 1}
												<span class="word-diff">
													({calculateWordDiff(version.wordCount, versions[index + 1].wordCount)})
												</span>
											{/if}
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="version-actions">
							<Button variant="ghost" size="sm" onclick={() => handlePreview(version)}>
								<Icon icon="eye" class="h-4 w-4 mr-1" />
								預覽
							</Button>

							{#if !version.isCurrent}
								<Button variant="outline" size="sm" onclick={() => handleRestore(version.id)}>
									<Icon icon="rotate-ccw" class="h-4 w-4 mr-1" />
									恢復
								</Button>
							{/if}
						</div>
					</div>

					{#if expandedVersions.has(version.id)}
						<div class="version-details">
							<div class="version-summary">
								<h4 class="font-medium mb-1">更改摘要</h4>
								<p class="text-sm text-muted-foreground">{version.summary}</p>
							</div>

							{#if version.title !== note.title}
								<div class="title-change">
									<h4 class="font-medium mb-1">標題變更</h4>
									<div class="text-sm">
										<div class="old-title">舊：{version.title}</div>
										<div class="new-title">新：{note.title}</div>
									</div>
								</div>
							{/if}
						</div>
					{/if}
				</div>
			{/each}
		</div>

		<!-- 預覽面板 -->
		{#if selectedVersion}
			<div class="preview-panel">
				<div class="preview-header">
					<h4 class="font-semibold">版本預覽 - v{selectedVersion.version}</h4>
					<Button variant="ghost" size="sm" onclick={() => (selectedVersion = null)}>
						<Icon icon="x" class="h-4 w-4" />
					</Button>
				</div>

				<div class="preview-content">
					<h1 class="text-2xl font-bold mb-4">{selectedVersion.title}</h1>
					<MarkdownRenderer content={selectedVersion.content} />
				</div>
			</div>
		{/if}
	</div>
</Card>

<style>
	.version-history {
		max-width: 100%;
		max-height: 80vh;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}

	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1rem;
		border-bottom: 1px solid hsl(var(--border));
	}

	.content {
		display: flex;
		flex: 1;
		overflow: hidden;
	}

	.version-list {
		flex: 1;
		overflow-y: auto;
		padding: 1rem;
		border-right: 1px solid hsl(var(--border));
		min-width: 400px;
	}

	.version-item {
		border: 1px solid hsl(var(--border));
		border-radius: 0.5rem;
		margin-bottom: 0.75rem;
		overflow: hidden;
		transition: all 0.2s ease;
	}

	.version-item:hover {
		border-color: hsl(var(--primary) / 0.3);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	}

	.version-item.current {
		border-color: hsl(var(--primary));
		background: hsl(var(--primary) / 0.05);
	}

	.version-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0.75rem;
	}

	.expand-button {
		background: none;
		border: none;
		padding: 0.25rem;
		border-radius: 0.25rem;
		cursor: pointer;
		color: hsl(var(--muted-foreground));
		transition: background-color 0.2s ease;
	}

	.expand-button:hover {
		background: hsl(var(--muted));
	}

	.version-info {
		flex: 1;
		margin-left: 0.5rem;
	}

	.version-number {
		font-family: monospace;
		font-weight: 600;
		color: hsl(var(--primary));
	}

	.change-type {
		display: inline-flex;
		align-items: center;
		padding: 0.125rem 0.375rem;
		border-radius: 0.25rem;
		font-size: 0.75rem;
		font-weight: 500;
	}

	.current-badge {
		display: inline-flex;
		align-items: center;
		padding: 0.125rem 0.375rem;
		border-radius: 0.25rem;
		font-size: 0.75rem;
		font-weight: 500;
		background: hsl(var(--primary));
		color: hsl(var(--primary-foreground));
	}

	.version-meta {
		margin-top: 0.25rem;
	}

	.word-diff {
		color: hsl(var(--muted-foreground));
		font-weight: 500;
	}

	.version-actions {
		display: flex;
		gap: 0.5rem;
	}

	.version-details {
		padding: 0.75rem;
		border-top: 1px solid hsl(var(--border));
		background: hsl(var(--muted) / 0.3);
		space-y: 0.75rem;
	}

	.version-summary,
	.title-change {
		margin-bottom: 0.75rem;
	}

	.old-title {
		color: hsl(var(--destructive));
		text-decoration: line-through;
	}

	.new-title {
		color: hsl(var(--primary));
		font-weight: 500;
	}

	.preview-panel {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}

	.preview-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1rem;
		border-bottom: 1px solid hsl(var(--border));
		background: hsl(var(--muted) / 0.3);
	}

	.preview-content {
		flex: 1;
		overflow-y: auto;
		padding: 1rem;
	}

	/* 響應式設計 */
	@media (max-width: 768px) {
		.content {
			flex-direction: column;
		}

		.version-list {
			border-right: none;
			border-bottom: 1px solid hsl(var(--border));
			min-width: auto;
			max-height: 50vh;
		}

		.version-header {
			flex-direction: column;
			align-items: flex-start;
			gap: 0.5rem;
		}

		.version-actions {
			align-self: flex-end;
		}
	}

	/* 滾動條樣式 */
	.version-list::-webkit-scrollbar,
	.preview-content::-webkit-scrollbar {
		width: 6px;
	}

	.version-list::-webkit-scrollbar-track,
	.preview-content::-webkit-scrollbar-track {
		background: hsl(var(--muted));
	}

	.version-list::-webkit-scrollbar-thumb,
	.preview-content::-webkit-scrollbar-thumb {
		background: hsl(var(--muted-foreground) / 0.3);
		border-radius: 3px;
	}

	.version-list::-webkit-scrollbar-thumb:hover,
	.preview-content::-webkit-scrollbar-thumb:hover {
		background: hsl(var(--muted-foreground) / 0.5);
	}
</style>
