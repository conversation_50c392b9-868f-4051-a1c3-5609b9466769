<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { Button, Icon } from '$components/ui';
	import { searchStore } from '$stores/search';
	import { clickOutside } from '$lib/actions/clickOutside';

	interface Props {
		placeholder?: string;
		value?: string;
		showSuggestions?: boolean;
		showRecentSearches?: boolean;
		autofocus?: boolean;
	}

	let {
		placeholder = '搜索筆記...',
		value = $bindable(''),
		showSuggestions = true,
		showRecentSearches = true,
		autofocus = false
	}: Props = $props();

	// 內部狀態來管理輸入值
	let internalValue = $state(value);

	const dispatch = createEventDispatcher<{
		search: { query: string };
		clear: void;
		select: { query: string };
	}>();

	let inputElement: HTMLInputElement;
	let showDropdown = $state(false);
	let highlightedIndex = $state(-1);

	const suggestions = $derived($searchStore.suggestions);
	const recentSearches = $derived($searchStore.recentSearches);
	const popularTags = $derived($searchStore.popularTags);

	// 組合建議列表
	const combinedSuggestions = $derived([
		...suggestions.map(s => ({ type: 'suggestion', value: s, label: s })),
		...(showRecentSearches && internalValue.length === 0
			? recentSearches.map(s => ({ type: 'recent', value: s, label: s }))
			: []),
		...(internalValue.length === 0
			? popularTags
					.slice(0, 5)
					.map(t => ({ type: 'tag', value: t.name, label: `${t.name} (${t.count})` }))
			: [])
	]);

	onMount(() => {
		if (autofocus && inputElement) {
			inputElement.focus();
		}
	});

	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		internalValue = target.value;
		value = internalValue; // 同步到 bindable prop
		highlightedIndex = -1;

		if (internalValue.length >= 2) {
			searchStore.getSuggestions(internalValue);
		}

		showDropdown = true;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (!showDropdown || combinedSuggestions.length === 0) {
			if (event.key === 'Enter') {
				handleSearch();
			}
			return;
		}

		switch (event.key) {
			case 'ArrowDown':
				event.preventDefault();
				highlightedIndex = Math.min(highlightedIndex + 1, combinedSuggestions.length - 1);
				break;
			case 'ArrowUp':
				event.preventDefault();
				highlightedIndex = Math.max(highlightedIndex - 1, -1);
				break;
			case 'Enter':
				event.preventDefault();
				if (highlightedIndex >= 0) {
					selectSuggestion(combinedSuggestions[highlightedIndex]);
				} else {
					handleSearch();
				}
				break;
			case 'Escape':
				showDropdown = false;
				highlightedIndex = -1;
				inputElement.blur();
				break;
		}
	}

	function handleSearch() {
		if (internalValue.trim()) {
			dispatch('search', { query: internalValue.trim() });
			showDropdown = false;
		}
	}

	function handleClear() {
		internalValue = '';
		value = '';
		showDropdown = false;
		highlightedIndex = -1;
		dispatch('clear');
		inputElement.focus();
	}

	function selectSuggestion(suggestion: { type: string; value: string; label: string }) {
		internalValue = suggestion.value;
		value = suggestion.value;
		showDropdown = false;
		highlightedIndex = -1;
		dispatch('select', { query: suggestion.value });
		dispatch('search', { query: suggestion.value });
	}

	function handleFocus() {
		showDropdown = true;
	}

	function handleClickOutside() {
		showDropdown = false;
		highlightedIndex = -1;
	}

	function getIconNameForType(type: string) {
		switch (type) {
			case 'recent':
				return 'clock';
			case 'tag':
				return 'trending-up';
			default:
				return 'search';
		}
	}

	function getTypeLabel(type: string) {
		switch (type) {
			case 'recent':
				return '最近搜索';
			case 'tag':
				return '熱門標籤';
			case 'suggestion':
				return '建議';
			default:
				return '';
		}
	}
</script>

<div class="relative w-full" use:clickOutside onclickOutside={handleClickOutside}>
	<!-- 搜索輸入框 -->
	<div class="relative">
		<Icon
			icon="search"
			class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
		/>
		<input
			bind:this={inputElement}
			bind:value={internalValue}
			type="text"
			{placeholder}
			class="input pl-10 pr-10"
			oninput={handleInput}
			onkeydown={handleKeydown}
			onfocus={handleFocus}
		/>
		{#if internalValue}
			<button
				type="button"
				class="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
				onclick={handleClear}
			>
				<Icon icon="x" class="h-4 w-4" />
			</button>
		{/if}
	</div>

	<!-- 建議下拉框 -->
	{#if showDropdown && combinedSuggestions.length > 0}
		<div
			class="absolute top-full left-0 right-0 mt-1 bg-popover border border-border rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
		>
			{#each combinedSuggestions as suggestion, index}
				{@const iconName = getIconNameForType(suggestion.type)}
				<button
					type="button"
					class="w-full px-3 py-2 text-left hover:bg-accent hover:text-accent-foreground flex items-center gap-2 {highlightedIndex ===
					index
						? 'bg-accent text-accent-foreground'
						: ''}"
					onclick={() => selectSuggestion(suggestion)}
				>
					<Icon icon={iconName} class="h-4 w-4 text-muted-foreground" />
					<div class="flex-1 min-w-0">
						<div class="text-sm">{suggestion.label}</div>
						{#if suggestion.type !== 'suggestion'}
							<div class="text-xs text-muted-foreground">{getTypeLabel(suggestion.type)}</div>
						{/if}
					</div>
				</button>
			{/each}

			<!-- 清除最近搜索 -->
			{#if recentSearches.length > 0 && internalValue.length === 0}
				<div class="border-t border-border">
					<button
						type="button"
						class="w-full px-3 py-2 text-left text-sm text-muted-foreground hover:bg-accent hover:text-accent-foreground"
						onclick={() => searchStore.clearRecentSearches()}
					>
						清除搜索歷史
					</button>
				</div>
			{/if}
		</div>
	{/if}
</div>

<style>
	/* 確保下拉框在其他元素之上 */
	:global(.search-dropdown) {
		z-index: 1000;
	}
</style>
