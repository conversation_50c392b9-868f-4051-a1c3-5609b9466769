<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { tv } from 'tailwind-variants';

	const itemVariants = tv({
		base: 'relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
		variants: {
			variant: {
				default: 'hover:bg-accent hover:text-accent-foreground',
				destructive: 'text-destructive hover:bg-destructive hover:text-destructive-foreground'
			}
		},
		defaultVariants: {
			variant: 'default'
		}
	});

	interface Props {
		value?: any;
		disabled?: boolean;
		variant?: 'default' | 'destructive';
		class?: string;
		children?: any;
	}

	let {
		value = undefined,
		disabled = false,
		variant = 'default',
		class: className = '',
		children
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		select: { value: any };
	}>();

	let computedClass = $derived(itemVariants({ variant, class: className }));

	const handleClick = () => {
		if (disabled) return;
		dispatch('select', { value });
	};

	const handleKeydown = (event: KeyboardEvent) => {
		if (disabled) return;
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			dispatch('select', { value });
		}
	};
</script>

<div
	class={computedClass}
	role="menuitem"
	tabindex={disabled ? -1 : 0}
	data-disabled={disabled ? 'true' : undefined}
	onclick={handleClick}
	onkeydown={handleKeydown}
>
	{@render children?.()}
</div>
