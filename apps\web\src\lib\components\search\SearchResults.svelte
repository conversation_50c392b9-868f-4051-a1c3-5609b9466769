<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { Icon } from '$components/ui';

	// Props
	interface Props {
		results?: any[];
		viewMode?: 'grid' | 'list';
		isSearching?: boolean;
		query?: string;
	}

	const { results = [], viewMode = 'grid', isSearching = false, query = '' }: Props = $props();

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		'note-click': { noteId: string };
		'note-edit': { noteId: string };
		'note-preview': { noteId: string };
	}>();

	// Highlight search terms in text
	const highlightText = (text: string, searchQuery: string): string => {
		if (!searchQuery.trim()) return text;

		const terms = searchQuery.trim().split(/\s+/);
		let highlightedText = text;

		terms.forEach(term => {
			const regex = new RegExp(`(${term})`, 'gi');
			highlightedText = highlightedText.replace(
				regex,
				'<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>'
			);
		});

		return highlightedText;
	};

	// Truncate text
	const truncateText = (text: string, maxLength: number): string => {
		if (text.length <= maxLength) return text;
		return text.substring(0, maxLength) + '...';
	};

	// Format date
	const formatDate = (date: string | Date): string => {
		const d = new Date(date);
		return d.toLocaleDateString('zh-TW', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	};

	// Get priority color
	const getPriorityColor = (priority: string): string => {
		const colors = {
			low: 'text-blue-600',
			medium: 'text-green-600',
			high: 'text-orange-600',
			urgent: 'text-red-600'
		};
		return colors[priority as keyof typeof colors] || 'text-gray-600';
	};

	// Get status color
	const getStatusColor = (status: string): string => {
		const colors = {
			draft: 'text-yellow-600 bg-yellow-50',
			published: 'text-green-600 bg-green-50',
			archived: 'text-gray-600 bg-gray-50'
		};
		return colors[status as keyof typeof colors] || 'text-gray-600 bg-gray-50';
	};

	// Handle note click
	const handleNoteClick = (noteId: string) => {
		dispatch('note-click', { noteId });
	};

	// Handle note edit
	const handleNoteEdit = (noteId: string, event: Event) => {
		event.stopPropagation();
		dispatch('note-edit', { noteId });
	};

	// Handle note preview
	const handleNotePreview = (noteId: string, event: Event) => {
		event.stopPropagation();
		dispatch('note-preview', { noteId });
	};

	// Mock data for demonstration
	const mockResults = $derived(
		results.length > 0
			? results
			: [
					{
						id: '1',
						title: 'Svelte 學習筆記',
						content: 'Svelte 是一個現代化的前端框架，它通過編譯時優化提供了出色的性能...',
						category: '學習',
						tags: ['svelte', 'frontend', 'javascript'],
						status: 'published',
						priority: 'medium',
						createdAt: new Date('2024-01-15'),
						updatedAt: new Date('2024-01-20')
					},
					{
						id: '2',
						title: 'TypeScript 最佳實踐',
						content: 'TypeScript 為 JavaScript 添加了靜態類型檢查，這裡記錄一些最佳實踐...',
						category: '工作',
						tags: ['typescript', 'javascript', 'best-practices'],
						status: 'draft',
						priority: 'high',
						createdAt: new Date('2024-01-10'),
						updatedAt: new Date('2024-01-18')
					}
				]
	);
</script>

<div class="search-results p-4">
	{#if isSearching}
		<!-- Loading State -->
		<div class="flex items-center justify-center py-12">
			<div class="text-center">
				<div class="spinner h-8 w-8 mx-auto mb-4"></div>
				<p class="text-muted-foreground">搜尋中...</p>
			</div>
		</div>
	{:else if mockResults.length === 0}
		<!-- No Results -->
		<div class="flex flex-col items-center justify-center py-12 text-center">
			<Icon icon="search" class="h-16 w-16 text-muted-foreground mb-4" />
			<h3 class="text-xl font-semibold mb-2">沒有找到相關筆記</h3>
			<p class="text-muted-foreground mb-4 max-w-md">
				{#if query}
					沒有找到包含 "{query}" 的筆記。請嘗試使用不同的關鍵字或調整篩選條件。
				{:else}
					請輸入搜尋關鍵字或調整篩選條件來查找筆記。
				{/if}
			</p>

			<div class="text-sm text-muted-foreground">
				<p class="mb-2">搜尋建議：</p>
				<ul class="text-left space-y-1">
					<li>• 檢查拼寫是否正確</li>
					<li>• 嘗試使用更通用的關鍵字</li>
					<li>• 減少篩選條件</li>
					<li>• 使用標籤或分類進行搜尋</li>
				</ul>
			</div>
		</div>
	{:else}
		<!-- Results -->
		<div class="results-container">
			{#if viewMode === 'grid'}
				<!-- Grid View -->
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{#each mockResults as note (note.id)}
						<Card
							class="p-4 hover:shadow-lg transition-shadow cursor-pointer group"
							onclick={() => handleNoteClick(note.id)}
						>
							<!-- Header -->
							<div class="flex items-start justify-between mb-3">
								<div class="flex-1 min-w-0">
									<h3 class="font-semibold text-lg mb-1 line-clamp-2">
										{@html highlightText(note.title, query)}
									</h3>

									<div class="flex items-center space-x-2 text-sm text-muted-foreground">
										{#if note.category}
											<div class="flex items-center">
												<Icon icon="folder-open" class="h-3 w-3 mr-1" />
												<span>{note.category}</span>
											</div>
										{/if}

										<span class="px-2 py-0.5 rounded-full text-xs {getStatusColor(note.status)}">
											{note.status === 'draft'
												? '草稿'
												: note.status === 'published'
													? '已發布'
													: '已封存'}
										</span>
									</div>
								</div>

								<div
									class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity"
								>
									<Button
										variant="ghost"
										size="sm"
										class="h-8 w-8 p-0"
										onclick={e => handleNotePreview(note.id, e)}
										title="預覽"
									>
										<Icon icon="eye" class="h-4 w-4" />
									</Button>
									<Button
										variant="ghost"
										size="sm"
										class="h-8 w-8 p-0"
										onclick={e => handleNoteEdit(note.id, e)}
										title="編輯"
									>
										<Icon icon="edit" class="h-4 w-4" />
									</Button>
								</div>
							</div>

							<!-- Content Preview -->
							<div class="mb-3">
								<p class="text-sm text-muted-foreground line-clamp-3">
									{@html highlightText(truncateText(note.content, 150), query)}
								</p>
							</div>

							<!-- Tags -->
							{#if note.tags && note.tags.length > 0}
								<div class="flex flex-wrap gap-1 mb-3">
									{#each note.tags.slice(0, 3) as tag}
										<span
											class="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-primary/10 text-primary"
										>
											#{tag}
										</span>
									{/each}
									{#if note.tags.length > 3}
										<span class="text-xs text-muted-foreground">+{note.tags.length - 3} 更多</span>
									{/if}
								</div>
							{/if}

							<!-- Footer -->
							<div class="flex items-center justify-between text-xs text-muted-foreground">
								<div class="flex items-center">
									<Icon icon="clock" class="h-3 w-3 mr-1" />
									<span>{formatDate(note.updatedAt)}</span>
								</div>

								{#if note.priority !== 'medium'}
									<div class="flex items-center {getPriorityColor(note.priority)}">
										<Icon icon="alert-circle" class="h-3 w-3 mr-1" />
										<span class="capitalize">{note.priority}</span>
									</div>
								{/if}
							</div>
						</Card>
					{/each}
				</div>
			{:else}
				<!-- List View -->
				<div class="space-y-3">
					{#each mockResults as note (note.id)}
						<Card
							class="p-4 hover:shadow-md transition-shadow cursor-pointer group"
							onclick={() => handleNoteClick(note.id)}
						>
							<div class="flex items-start space-x-4">
								<!-- Icon -->
								<div class="flex-shrink-0 mt-1">
									<Icon icon="file-text" class="h-5 w-5 text-muted-foreground" />
								</div>

								<!-- Content -->
								<div class="flex-1 min-w-0">
									<div class="flex items-start justify-between">
										<div class="flex-1 min-w-0">
											<h3 class="font-semibold text-lg mb-1">
												{@html highlightText(note.title, query)}
											</h3>

											<p class="text-sm text-muted-foreground mb-2 line-clamp-2">
												{@html highlightText(truncateText(note.content, 200), query)}
											</p>

											<div class="flex items-center space-x-4 text-xs text-muted-foreground">
												{#if note.category}
													<div class="flex items-center">
														<Icon icon="folder-open" class="h-3 w-3 mr-1" />
														<span>{note.category}</span>
													</div>
												{/if}

												<div class="flex items-center">
													<Icon icon="calendar" class="h-3 w-3 mr-1" />
													<span>{formatDate(note.updatedAt)}</span>
												</div>

												<span class="px-2 py-0.5 rounded-full {getStatusColor(note.status)}">
													{note.status === 'draft'
														? '草稿'
														: note.status === 'published'
															? '已發布'
															: '已封存'}
												</span>

												{#if note.priority !== 'medium'}
													<span
														class="px-2 py-0.5 rounded-full {getPriorityColor(
															note.priority
														)} bg-current/10"
													>
														{note.priority}
													</span>
												{/if}
											</div>
										</div>

										<!-- Actions -->
										<div
											class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity"
										>
											<Button
												variant="ghost"
												size="sm"
												class="h-8 w-8 p-0"
												onclick={e => handleNotePreview(note.id, e)}
												title="預覽"
											>
												<Icon icon="eye" class="h-4 w-4" />
											</Button>
											<Button
												variant="ghost"
												size="sm"
												class="h-8 w-8 p-0"
												onclick={e => handleNoteEdit(note.id, e)}
												title="編輯"
											>
												<Icon icon="edit" class="h-4 w-4" />
											</Button>
										</div>
									</div>

									<!-- Tags -->
									{#if note.tags && note.tags.length > 0}
										<div class="flex flex-wrap gap-1 mt-2">
											{#each note.tags as tag}
												<span
													class="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-primary/10 text-primary"
												>
													#{tag}
												</span>
											{/each}
										</div>
									{/if}
								</div>
							</div>
						</Card>
					{/each}
				</div>
			{/if}
		</div>
	{/if}
</div>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	:global(mark) {
		padding: 0.1em 0.2em;
		border-radius: 0.2em;
	}
</style>
