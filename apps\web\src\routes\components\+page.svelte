<script lang="ts">
	import {
		Button,
		Card,
		Input,
		Textarea,
		Badge,
		Modal,
		Dropdown,
		DropdownItem,
		Loading
	} from '$components/ui';
	import { toastStore } from '$stores/toast';
	import { Plus, Settings, User, LogOut } from 'lucide-svelte';

	let showModal = $state(false);
	let inputValue = $state('');
	let textareaValue = $state('');
	let selectedOption = $state('Option 1');

	const handleToastDemo = (variant: 'success' | 'error' | 'warning' | 'info') => {
		switch (variant) {
			case 'success':
				toastStore.success('成功！', '操作已成功完成。');
				break;
			case 'error':
				toastStore.error('錯誤！', '發生了一個錯誤，請重試。');
				break;
			case 'warning':
				toastStore.warning('警告！', '請注意這個操作可能有風險。');
				break;
			case 'info':
				toastStore.info('信息', '這是一個信息提示。');
				break;
		}
	};

	const handleDropdownSelect = (event: CustomEvent) => {
		selectedOption = event.detail.value;
	};
</script>

<svelte:head>
	<title>UI 組件展示 - Life Note</title>
</svelte:head>

<div class="container mx-auto px-4 py-8 space-y-12">
	<div class="text-center">
		<h1 class="text-4xl font-bold mb-4">UI 組件庫</h1>
		<p class="text-xl text-muted-foreground">Life Note 設計系統組件展示</p>
	</div>

	<!-- Buttons -->
	<section>
		<h2 class="text-2xl font-semibold mb-6">按鈕 (Buttons)</h2>
		<Card class="p-6">
			<div class="space-y-4">
				<div class="flex flex-wrap gap-4">
					<Button variant="default">默認按鈕</Button>
					<Button variant="secondary">次要按鈕</Button>
					<Button variant="outline">輪廓按鈕</Button>
					<Button variant="ghost">幽靈按鈕</Button>
					<Button variant="destructive">危險按鈕</Button>
				</div>

				<div class="flex flex-wrap gap-4">
					<Button size="sm">小按鈕</Button>
					<Button size="default">默認大小</Button>
					<Button size="lg">大按鈕</Button>
					<Button size="icon"><Plus class="h-4 w-4" /></Button>
				</div>

				<div class="flex flex-wrap gap-4">
					<Button disabled>禁用按鈕</Button>
					<Button>
						<Settings class="mr-2 h-4 w-4" />
						帶圖標
					</Button>
				</div>
			</div>
		</Card>
	</section>

	<!-- Inputs -->
	<section>
		<h2 class="text-2xl font-semibold mb-6">輸入框 (Inputs)</h2>
		<Card class="p-6">
			<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
				<Input
					label="用戶名"
					placeholder="請輸入用戶名"
					bind:value={inputValue}
					hint="用戶名必須是唯一的"
				/>

				<Input label="密碼" type="password" placeholder="請輸入密碼" error="密碼長度至少8位" />

				<Input label="郵箱" type="email" placeholder="請輸入郵箱地址" variant="success" />

				<Input label="禁用輸入" placeholder="這是禁用的輸入框" disabled />
			</div>

			<div class="mt-6">
				<Textarea
					label="描述"
					placeholder="請輸入詳細描述..."
					bind:value={textareaValue}
					hint="最多500字符"
					rows={4}
				/>
			</div>
		</Card>
	</section>

	<!-- Badges -->
	<section>
		<h2 class="text-2xl font-semibold mb-6">標籤 (Badges)</h2>
		<Card class="p-6">
			<div class="space-y-4">
				<div class="flex flex-wrap gap-2">
					<Badge variant="default">默認</Badge>
					<Badge variant="secondary">次要</Badge>
					<Badge variant="success">成功</Badge>
					<Badge variant="warning">警告</Badge>
					<Badge variant="destructive">錯誤</Badge>
					<Badge variant="outline">輪廓</Badge>
				</div>

				<div class="flex flex-wrap gap-2">
					<Badge size="sm">小標籤</Badge>
					<Badge size="default">默認大小</Badge>
					<Badge size="lg">大標籤</Badge>
				</div>

				<div class="flex flex-wrap gap-2">
					<Badge variant="secondary" removable>可移除</Badge>
					<Badge variant="success" removable>可移除成功</Badge>
					<Badge variant="warning" removable>可移除警告</Badge>
				</div>
			</div>
		</Card>
	</section>

	<!-- Dropdown -->
	<section>
		<h2 class="text-2xl font-semibold mb-6">下拉菜單 (Dropdown)</h2>
		<Card class="p-6">
			<div class="space-y-4">
				<div class="w-64">
					<Dropdown>
						<span slot="trigger">{selectedOption}</span>
						<DropdownItem value="Option 1" on:select={handleDropdownSelect}>
							<User class="mr-2 h-4 w-4" />
							選項 1
						</DropdownItem>
						<DropdownItem value="Option 2" on:select={handleDropdownSelect}>
							<Settings class="mr-2 h-4 w-4" />
							選項 2
						</DropdownItem>
						<DropdownItem value="Option 3" on:select={handleDropdownSelect}>選項 3</DropdownItem>
						<DropdownItem variant="destructive" on:select={handleDropdownSelect}>
							<LogOut class="mr-2 h-4 w-4" />
							登出
						</DropdownItem>
					</Dropdown>
				</div>
			</div>
		</Card>
	</section>

	<!-- Loading -->
	<section>
		<h2 class="text-2xl font-semibold mb-6">加載狀態 (Loading)</h2>
		<Card class="p-6">
			<div class="space-y-6">
				<div class="flex flex-wrap gap-4 items-center">
					<Loading size="xs" />
					<Loading size="sm" />
					<Loading size="default" />
					<Loading size="lg" />
					<Loading size="xl" />
				</div>

				<div class="flex flex-wrap gap-4 items-center">
					<Loading variant="default" text="加載中..." />
					<Loading variant="secondary" text="處理中..." />
					<Loading variant="muted" text="請稍候..." />
				</div>
			</div>
		</Card>
	</section>

	<!-- Toast Demo -->
	<section>
		<h2 class="text-2xl font-semibold mb-6">通知 (Toast)</h2>
		<Card class="p-6">
			<div class="flex flex-wrap gap-4">
				<Button variant="default" onclick={() => handleToastDemo('success')}>成功通知</Button>
				<Button variant="destructive" onclick={() => handleToastDemo('error')}>錯誤通知</Button>
				<Button variant="outline" onclick={() => handleToastDemo('warning')}>警告通知</Button>
				<Button variant="secondary" onclick={() => handleToastDemo('info')}>信息通知</Button>
			</div>
		</Card>
	</section>

	<!-- Modal Demo -->
	<section>
		<h2 class="text-2xl font-semibold mb-6">模態框 (Modal)</h2>
		<Card class="p-6">
			<Button onclick={() => (showModal = true)}>打開模態框</Button>
		</Card>
	</section>
</div>

<!-- Modal -->
<Modal
	bind:open={showModal}
	title="示例模態框"
	description="這是一個示例模態框，展示基本功能。"
	size="lg"
>
	<div class="space-y-4">
		<p>這是模態框的內容區域。您可以在這裡放置任何內容。</p>

		<Input label="示例輸入" placeholder="在模態框中輸入..." />

		<div class="flex items-center space-x-2">
			<Badge variant="success">標籤1</Badge>
			<Badge variant="warning">標籤2</Badge>
			<Badge variant="secondary">標籤3</Badge>
		</div>
	</div>

	<div slot="footer">
		<Button variant="outline" onclick={() => (showModal = false)}>取消</Button>
		<Button onclick={() => (showModal = false)}>確定</Button>
	</div>
</Modal>
