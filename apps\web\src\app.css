@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
	/* Spacing */
	--spacing-xs: 0.25rem;
	--spacing-sm: 0.5rem;
	--spacing-md: 1rem;
	--spacing-lg: 1.5rem;
	--spacing-xl: 2rem;
	--spacing-2xl: 3rem;

	/* Border radius */
	--radius-sm: 0.25rem;
	--radius-md: 0.375rem;
	--radius-lg: 0.5rem;
	--radius-xl: 0.75rem;

	/* Shadows */
	--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
	--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

	/* Transitions */
	--transition-fast: 150ms ease-in-out;
	--transition-normal: 250ms ease-in-out;
	--transition-slow: 350ms ease-in-out;

	/* Z-index layers */
	--z-dropdown: 1000;
	--z-sticky: 1020;
	--z-fixed: 1030;
	--z-modal-backdrop: 1040;
	--z-modal: 1050;
	--z-popover: 1060;
	--z-tooltip: 1070;
	--z-toast: 1080;
}

/* Base layer customizations */
@layer base {
	/* Improved focus styles */
	*:focus-visible {
		@apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
	}

	/* Smooth scrolling for reduced motion users */
	@media (prefers-reduced-motion: no-preference) {
		html {
			scroll-behavior: smooth;
		}
	}

	/* Custom scrollbar */
	::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	::-webkit-scrollbar-track {
		@apply bg-gray-100 dark:bg-gray-800;
	}

	::-webkit-scrollbar-thumb {
		@apply bg-gray-300 dark:bg-gray-600 rounded-full;
	}

	::-webkit-scrollbar-thumb:hover {
		@apply bg-gray-400 dark:bg-gray-500;
	}

	/* Selection styles */
	::selection {
		@apply bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100;
	}
}

/* Component layer utilities */
@layer components {
	/* Button variants */
	.btn {
		@apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
	}

	.btn-primary {
		@apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800;
	}

	.btn-secondary {
		@apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700;
	}

	.btn-outline {
		@apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700;
	}

	.btn-ghost {
		@apply btn text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800;
	}

	.btn-sm {
		@apply px-3 py-1.5 text-xs;
	}

	.btn-lg {
		@apply px-6 py-3 text-base;
	}

	/* Card component */
	.card {
		@apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
	}

	.card-header {
		@apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
	}

	.card-body {
		@apply px-6 py-4;
	}

	.card-footer {
		@apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50;
	}

	/* Input styles */
	.input {
		@apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-white dark:border-gray-600 dark:placeholder-gray-500 dark:text-black dark:focus:ring-primary-500 dark:focus:border-primary-500;
	}

	.input-error {
		@apply border-red-300 focus:ring-red-500 focus:border-red-500 dark:border-red-600;
	}

	/* Select styles */
	.select {
		@apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-white dark:border-gray-600 dark:text-black dark:focus:ring-primary-500 dark:focus:border-primary-500;
	}

	/* Badge styles */
	.badge {
		@apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
	}

	.badge-primary {
		@apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
	}

	.badge-secondary {
		@apply badge bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200;
	}

	.badge-success {
		@apply badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
	}

	.badge-warning {
		@apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
	}

	.badge-error {
		@apply badge bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
	}

	/* Loading states */
	.loading-spinner {
		@apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
	}

	.loading-pulse {
		@apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
	}

	/* Utility classes */
	.text-balance {
		text-wrap: balance;
	}

	.text-pretty {
		text-wrap: pretty;
	}
}

/* Utility layer customizations */
@layer utilities {
	/* Custom animations */
	.animate-fade-in {
		animation: fadeIn 0.5s ease-in-out;
	}

	.animate-slide-up {
		animation: slideUp 0.3s ease-out;
	}

	.animate-slide-down {
		animation: slideDown 0.3s ease-out;
	}

	.animate-scale-in {
		animation: scaleIn 0.2s ease-out;
	}

	/* Glass morphism effect */
	.glass {
		@apply backdrop-blur-sm bg-white/80 dark:bg-gray-900/80 border border-white/20 dark:border-gray-800/20;
	}

	/* Gradient text */
	.gradient-text {
		@apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
	}

	/* Hide scrollbar but keep functionality */
	.scrollbar-hide {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}

	.scrollbar-hide::-webkit-scrollbar {
		display: none;
	}

	/* Custom focus ring */
	.focus-ring {
		@apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900;
	}
}

/* Animation keyframes */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(10px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes slideDown {
	from {
		transform: translateY(-10px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes scaleIn {
	from {
		transform: scale(0.95);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

/* Print styles */
@media print {
	.no-print {
		display: none !important;
	}

	.print-break-before {
		break-before: page;
	}

	.print-break-after {
		break-after: page;
	}

	.print-break-inside-avoid {
		break-inside: avoid;
	}
}
