<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Button, Icon } from '$components/ui';

	interface Props {
		tags?: Array<{ name: string }>;
		editable?: boolean;
		size?: 'sm' | 'md' | 'lg';
		variant?: 'default' | 'outline' | 'secondary';
		maxDisplay?: number; // 0 表示顯示所有標籤
		className?: string;
	}

	const {
		tags = [],
		editable = false,
		size = 'md',
		variant = 'default',
		maxDisplay = 0,
		className = ''
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		tagClick: { tag: { name: string } };
		tagRemove: { tag: { name: string } };
		showMore: void;
	}>();

	$: displayTags = maxDisplay > 0 ? tags.slice(0, maxDisplay) : tags;
	$: hiddenCount = maxDisplay > 0 ? Math.max(0, tags.length - maxDisplay) : 0;

	function handleTagClick(tag: { name: string }) {
		dispatch('tagClick', { tag });
	}

	function handleTagRemove(tag: { name: string }, event: Event) {
		event.stopPropagation();
		dispatch('tagRemove', { tag });
	}

	function handleShowMore() {
		dispatch('showMore');
	}

	function getTagClasses(): string {
		const baseClasses = 'inline-flex items-center gap-1 rounded-full font-medium transition-colors';

		const sizeClasses = {
			sm: 'px-2 py-1 text-xs',
			md: 'px-2.5 py-1.5 text-sm',
			lg: 'px-3 py-2 text-base'
		};

		const variantClasses = {
			default: 'bg-primary text-primary-foreground hover:bg-primary/80',
			outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
			secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
		};

		return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]}`;
	}

	function getRemoveButtonClasses(): string {
		const baseClasses = 'ml-1 rounded-full hover:bg-black/20 transition-colors';

		const sizeClasses = {
			sm: 'h-3 w-3',
			md: 'h-4 w-4',
			lg: 'h-5 w-5'
		};

		return `${baseClasses} ${sizeClasses[size]}`;
	}

	function getIconSize(): string {
		const sizes = {
			sm: 'h-2.5 w-2.5',
			md: 'h-3 w-3',
			lg: 'h-3.5 w-3.5'
		};
		return sizes[size];
	}
</script>

<div class="tag-list {className}">
	{#if tags.length === 0}
		<div class="flex items-center gap-1 text-muted-foreground text-sm">
			<Icon icon="tag" class="h-3 w-3" />
			<span>無標籤</span>
		</div>
	{:else}
		<div class="flex flex-wrap gap-1.5">
			{#each displayTags as tag (tag.name)}
				<span
					class={getTagClasses()}
					class:cursor-pointer={!editable}
					onclick={() => !editable && handleTagClick(tag)}
					onkeydown={e => {
						if (!editable && (e.key === 'Enter' || e.key === ' ')) {
							e.preventDefault();
							handleTagClick(tag);
						}
					}}
					role={editable ? 'none' : 'button'}
					tabindex={editable ? -1 : 0}
				>
					<Icon icon="tag" class={getIconSize()} />
					<span>{tag.name}</span>

					{#if editable}
						<button
							type="button"
							class={getRemoveButtonClasses()}
							onclick={e => handleTagRemove(tag, e)}
							title="移除標籤"
							aria-label="移除標籤 {tag.name}"
						>
							<Icon icon="x" class={getIconSize()} />
						</button>
					{/if}
				</span>
			{/each}

			{#if hiddenCount > 0}
				<button
					type="button"
					class="inline-flex items-center gap-1 px-2.5 py-1.5 text-sm rounded-full bg-muted text-muted-foreground hover:bg-muted/80 transition-colors"
					onclick={handleShowMore}
					title="顯示更多標籤"
				>
					<span>+{hiddenCount}</span>
				</button>
			{/if}
		</div>
	{/if}
</div>

<style>
	.tag-list {
		display: flex;
		align-items: flex-start;
		min-height: 1.5rem;
	}

	/* 確保標籤在小螢幕上正確換行 */
	@media (max-width: 640px) {
		.tag-list {
			align-items: flex-start;
		}
	}

	/* 聚焦樣式 */
	.tag-list span[role='button']:focus {
		outline: 2px solid hsl(var(--primary));
		outline-offset: 2px;
	}

	/* 動畫效果 */
	.tag-list span {
		animation: fadeIn 0.2s ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: scale(0.9);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	/* 懸停效果 */
	.tag-list span[role='button']:hover {
		transform: translateY(-1px);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	/* 移除按鈕懸停效果 */
	.tag-list button:hover {
		background-color: rgba(0, 0, 0, 0.2);
	}

	/* 暗色主題適配 */
	@media (prefers-color-scheme: dark) {
		.tag-list button:hover {
			background-color: rgba(255, 255, 255, 0.2);
		}
	}
</style>
