<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-svelte';
	import { Button } from '$components/ui';

	interface Props {
		page: number;
		totalPages: number;
		total: number;
		limit: number;
		hasNext: boolean;
		hasPrev: boolean;
	}

	const { page, totalPages, total, limit, hasNext, hasPrev }: Props = $props();

	const dispatch = createEventDispatcher<{
		pageChange: { page: number };
	}>();

	// 計算顯示的頁碼範圍
	const pageNumbers = $derived(calculatePageNumbers(page, totalPages));
	const startItem = $derived((page - 1) * limit + 1);
	const endItem = $derived(Math.min(page * limit, total));

	function calculatePageNumbers(currentPage: number, totalPages: number): (number | 'ellipsis')[] {
		const delta = 2; // 當前頁面前後顯示的頁數
		const range: (number | 'ellipsis')[] = [];

		// 總是顯示第一頁
		range.push(1);

		if (totalPages <= 7) {
			// 如果總頁數較少，顯示所有頁面
			for (let i = 2; i <= totalPages; i++) {
				range.push(i);
			}
		} else {
			// 計算當前頁面周圍的範圍
			const start = Math.max(2, currentPage - delta);
			const end = Math.min(totalPages - 1, currentPage + delta);

			// 如果開始位置不是第2頁，添加省略號
			if (start > 2) {
				range.push('ellipsis');
			}

			// 添加中間的頁面
			for (let i = start; i <= end; i++) {
				range.push(i);
			}

			// 如果結束位置不是倒數第2頁，添加省略號
			if (end < totalPages - 1) {
				range.push('ellipsis');
			}

			// 總是顯示最後一頁（如果總頁數大於1）
			if (totalPages > 1) {
				range.push(totalPages);
			}
		}

		return range;
	}

	function goToPage(targetPage: number) {
		if (targetPage >= 1 && targetPage <= totalPages && targetPage !== page) {
			dispatch('pageChange', { page: targetPage });
		}
	}

	function nextPage() {
		if (hasNext) {
			goToPage(page + 1);
		}
	}

	function prevPage() {
		if (hasPrev) {
			goToPage(page - 1);
		}
	}
</script>

{#if totalPages > 1}
	<div class="flex flex-col sm:flex-row items-center justify-between gap-4">
		<!-- 結果統計 -->
		<div class="text-sm text-muted-foreground">
			顯示第 <span class="font-medium">{startItem}</span> 到
			<span class="font-medium">{endItem}</span>
			項， 共 <span class="font-medium">{total}</span> 項結果
		</div>

		<!-- 分頁控制 -->
		<div class="flex items-center gap-1">
			<!-- 上一頁 -->
			<Button
				variant="outline"
				size="sm"
				disabled={!hasPrev}
				on:click={prevPage}
				class="flex items-center gap-1"
			>
				<ChevronLeft class="h-4 w-4" />
				<span class="hidden sm:inline">上一頁</span>
			</Button>

			<!-- 頁碼 -->
			<div class="flex items-center gap-1">
				{#each pageNumbers as pageNum}
					{#if pageNum === 'ellipsis'}
						<div class="px-3 py-2 text-muted-foreground">
							<MoreHorizontal class="h-4 w-4" />
						</div>
					{:else}
						<Button
							variant={pageNum === page ? 'default' : 'ghost'}
							size="sm"
							on:click={() => goToPage(pageNum)}
							class="min-w-[2.5rem]"
						>
							{pageNum}
						</Button>
					{/if}
				{/each}
			</div>

			<!-- 下一頁 -->
			<Button
				variant="outline"
				size="sm"
				disabled={!hasNext}
				on:click={nextPage}
				class="flex items-center gap-1"
			>
				<span class="hidden sm:inline">下一頁</span>
				<ChevronRight class="h-4 w-4" />
			</Button>
		</div>
	</div>
{/if}
