<script lang="ts">
	import { tv, type VariantProps } from 'tailwind-variants';

	const loadingVariants = tv({
		base: 'animate-spin rounded-full border-2 border-current border-t-transparent',
		variants: {
			size: {
				xs: 'h-3 w-3',
				sm: 'h-4 w-4',
				default: 'h-6 w-6',
				lg: 'h-8 w-8',
				xl: 'h-12 w-12'
			},
			variant: {
				default: 'text-primary',
				secondary: 'text-secondary',
				muted: 'text-muted-foreground',
				white: 'text-white'
			}
		},
		defaultVariants: {
			size: 'default',
			variant: 'default'
		}
	});

	const containerVariants = tv({
		base: 'flex items-center justify-center',
		variants: {
			fullscreen: {
				true: 'fixed inset-0 z-50 bg-background/80 backdrop-blur-sm',
				false: ''
			}
		},
		defaultVariants: {
			fullscreen: false
		}
	});

	type Size = VariantProps<typeof loadingVariants>['size'];
	type Variant = VariantProps<typeof loadingVariants>['variant'];

	interface Props {
		size?: Size;
		variant?: Variant;
		fullscreen?: boolean;
		text?: string;
		class?: string;
		children?: any;
	}

	let {
		size = 'default',
		variant = 'default',
		fullscreen = false,
		text = '',
		class: className = '',
		children
	}: Props = $props();

	let spinnerClass = $derived(loadingVariants({ size, variant, class: className }));
	let containerClass = $derived(containerVariants({ fullscreen }));
</script>

<div class={containerClass}>
	<div class="flex flex-col items-center space-y-2">
		<div class={spinnerClass} aria-label="Loading"></div>
		{#if text}
			<p class="text-sm text-muted-foreground">{text}</p>
		{/if}
		{@render children?.()}
	</div>
</div>
