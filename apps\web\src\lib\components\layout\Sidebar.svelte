<script lang="ts">
	import Icon from '@iconify/svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';

	import Button from '$components/ui/Button.svelte';
	import { noteStore } from '$stores/note';
	import { categoryStore } from '$stores/category';

	// Navigation items
	const navigationItems = [
		{ href: '/', label: '首頁', icon: 'lucide:home' },
		{ href: '/notes', label: '所有筆記', icon: 'lucide:file-text' },
		{ href: '/search', label: '搜尋', icon: 'lucide:search' },
		{ href: '/tags', label: '標籤', icon: 'lucide:tag' },
		{ href: '/dependencies', label: '依賴關係', icon: 'lucide:git-branch' },
		{ href: '/settings', label: '設定', icon: 'lucide:settings' },
		{ href: '/archive', label: '封存', icon: 'lucide:archive' },
		{ href: '/trash', label: '垃圾桶', icon: 'lucide:trash-2' }
	];

	// State
	let categoriesExpanded = true;
	let recentNotesExpanded = true;

	// Reactive values
	const currentPath = $derived($page.url.pathname);
	const categories = $derived($categoryStore.categories);
	const recentNotes = $derived($noteStore.recentNotes.slice(0, 5));

	// Event handlers
	const handleNavigation = (href: string) => {
		console.log('handleNavigation called with:', href);
		goto(href);
	};

	const toggleCategories = () => {
		categoriesExpanded = !categoriesExpanded;
	};

	const toggleRecentNotes = () => {
		recentNotesExpanded = !recentNotesExpanded;
	};

	const handleCategoryClick = (category: string) => {
		goto(`/notes?category=${encodeURIComponent(category)}`);
	};

	const handleNoteClick = (noteId: string) => {
		goto(`/notes/${noteId}`);
	};

	// Helper function to check if path is active
	const isActive = (href: string) => {
		if (href === '/') {
			return currentPath === '/';
		}
		return currentPath.startsWith(href);
	};
</script>

<nav class="flex h-full flex-col overflow-hidden">
	<!-- Main Navigation -->
	<div class="flex-1 overflow-y-auto p-4">
		<div class="space-y-1">
			{#each navigationItems as item}
				<!-- 使用 Svelte 5 正確的事件處理方式 -->
				<button
					class="inline-flex items-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 {isActive(
						item.href
					)
						? 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
						: 'hover:bg-accent hover:text-accent-foreground'} h-10 px-4 py-2 w-full justify-start"
					onclick={() => {
						console.log('Native button clicked:', item.label, item.href);
						handleNavigation(item.href);
					}}
				>
					<Icon icon={item.icon} class="mr-2 h-4 w-4" />
					{item.label}
				</button>
			{/each}
		</div>

		<!-- Categories Section -->
		<div class="mt-6">
			<Button
				variant="ghost"
				class="w-full justify-start p-2 text-sm font-medium text-muted-foreground hover:text-foreground"
				onclick={toggleCategories}
			>
				{#if categoriesExpanded}
					<Icon icon="lucide:chevron-down" class="mr-1 h-3 w-3" />
					<Icon icon="lucide:folder-open" class="mr-2 h-4 w-4" />
				{:else}
					<Icon icon="lucide:chevron-right" class="mr-1 h-3 w-3" />
					<Icon icon="lucide:folder" class="mr-2 h-4 w-4" />
				{/if}
				分類
			</Button>

			{#if categoriesExpanded}
				<div class="ml-4 mt-1 space-y-1">
					{#each categories as category}
						<Button
							variant="ghost"
							size="sm"
							class="w-full justify-start text-sm"
							onclick={() => handleCategoryClick(category.name)}
						>
							<span class="mr-2 h-2 w-2 rounded-full bg-primary"></span>
							{category.name}
							<span class="ml-auto text-xs text-muted-foreground">
								{category.count}
							</span>
						</Button>
					{/each}

					{#if categories.length === 0}
						<p class="px-2 py-1 text-xs text-muted-foreground">尚無分類</p>
					{/if}
				</div>
			{/if}
		</div>

		<!-- Recent Notes Section -->
		<div class="mt-6">
			<Button
				variant="ghost"
				class="w-full justify-start p-2 text-sm font-medium text-muted-foreground hover:text-foreground"
				onclick={toggleRecentNotes}
			>
				{#if recentNotesExpanded}
					<Icon icon="lucide:chevron-down" class="mr-1 h-3 w-3" />
				{:else}
					<Icon icon="lucide:chevron-right" class="mr-1 h-3 w-3" />
				{/if}
				<Icon icon="lucide:file-text" class="mr-2 h-4 w-4" />
				最近筆記
			</Button>

			{#if recentNotesExpanded}
				<div class="ml-4 mt-1 space-y-1">
					{#each recentNotes as note}
						<Button
							variant="ghost"
							size="sm"
							class="w-full justify-start text-sm"
							onclick={() => handleNoteClick(note.id.value)}
						>
							<span class="mr-2 h-2 w-2 rounded-full bg-muted-foreground"></span>
							<span class="truncate">{note.title}</span>
						</Button>
					{/each}

					{#if recentNotes.length === 0}
						<p class="px-2 py-1 text-xs text-muted-foreground">尚無最近筆記</p>
					{/if}
				</div>
			{/if}
		</div>
	</div>

	<!-- Bottom Section -->
	<div class="border-t p-4">
		<Button
			variant="ghost"
			class="w-full justify-start"
			onclick={() => {
				console.log('Settings button clicked');
				handleNavigation('/settings');
			}}
		>
			<Icon icon="lucide:settings" class="mr-2 h-4 w-4" />
			設定
		</Button>
	</div>
</nav>

<style>
	/* Custom scrollbar for sidebar */
	nav {
		scrollbar-width: thin;
		scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
	}

	nav::-webkit-scrollbar {
		width: 6px;
	}

	nav::-webkit-scrollbar-track {
		background: transparent;
	}

	nav::-webkit-scrollbar-thumb {
		background-color: hsl(var(--muted-foreground) / 0.3);
		border-radius: 3px;
	}

	nav::-webkit-scrollbar-thumb:hover {
		background-color: hsl(var(--muted-foreground) / 0.5);
	}

	/* Smooth transitions */
	:global(.sidebar-transition) {
		transition: all 0.2s ease-in-out;
	}

	/* Truncate long text */
	.truncate {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>
