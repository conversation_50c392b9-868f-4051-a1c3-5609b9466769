<script lang="ts">
	import { tv, type VariantProps } from 'tailwind-variants';
	import type { HTMLAttributes } from 'svelte/elements';

	const badgeVariants = tv({
		base: 'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
		variants: {
			variant: {
				default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
				secondary:
					'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
				destructive:
					'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
				success: 'border-transparent bg-green-500 text-white hover:bg-green-500/80',
				warning: 'border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80',
				outline: 'text-foreground border-border',
				ghost: 'border-transparent hover:bg-accent hover:text-accent-foreground'
			},
			size: {
				sm: 'px-1.5 py-0.5 text-xs',
				default: 'px-2.5 py-0.5 text-xs',
				lg: 'px-3 py-1 text-sm'
			}
		},
		defaultVariants: {
			variant: 'default',
			size: 'default'
		}
	});

	type Variant = VariantProps<typeof badgeVariants>['variant'];
	type Size = VariantProps<typeof badgeVariants>['size'];

	interface Props extends HTMLAttributes<HTMLDivElement> {
		variant?: Variant;
		size?: Size;
		class?: string;
		removable?: boolean;
	}

	let {
		variant = 'default',
		size = 'default',
		removable = false,
		class: className = '',
		...restProps
	}: Props = $props();

	let computedClass = $derived(badgeVariants({ variant, size, class: className }));

	const handleRemove = (event: Event) => {
		event.stopPropagation();
		// Dispatch custom remove event
		const removeEvent = new CustomEvent('remove', {
			detail: { event }
		});
		event.target?.dispatchEvent(removeEvent);
	};
</script>

<div class={computedClass} {...restProps}>
	<slot />

	{#if removable}
		<button
			type="button"
			class="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10 focus:outline-none focus:ring-1 focus:ring-inset focus:ring-white/20"
			onclick={handleRemove}
			aria-label="Remove badge"
		>
			<svg class="h-2 w-2" fill="currentColor" viewBox="0 0 8 8">
				<path
					d="M1.5 1.5l5 5m0-5l-5 5"
					stroke="currentColor"
					stroke-width="1.5"
					stroke-linecap="round"
				/>
			</svg>
		</button>
	{/if}
</div>
