<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { tv, type VariantProps } from 'tailwind-variants';
	import Button from './Button.svelte';
	import Icon from './Icon.svelte';

	const toastVariants = tv({
		base: 'group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all',
		variants: {
			variant: {
				default: 'border bg-background text-foreground',
				success:
					'border-green-500 bg-green-50 text-green-900 dark:bg-green-950 dark:text-green-100',
				error: 'border-red-500 bg-red-50 text-red-900 dark:bg-red-950 dark:text-red-100',
				warning:
					'border-yellow-500 bg-yellow-50 text-yellow-900 dark:bg-yellow-950 dark:text-yellow-100',
				info: 'border-blue-500 bg-blue-50 text-blue-900 dark:bg-blue-950 dark:text-blue-100'
			}
		},
		defaultVariants: {
			variant: 'default'
		}
	});

	type Variant = VariantProps<typeof toastVariants>['variant'];

	interface Props {
		variant?: Variant;
		title?: string;
		description?: string;
		duration?: number;
		closable?: boolean;
		class?: string;
	}

	const {
		variant = 'default',
		title = '',
		description = '',
		duration = 5000,
		closable = true,
		class: className = ''
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		close: void;
	}>();

	let visible = true;
	let timeoutId: number;

	const computedClass = $derived(toastVariants({ variant, class: className }));

	// Icon mapping
	const iconNames = {
		success: 'check-circle',
		error: 'alert-circle',
		warning: 'alert-triangle',
		info: 'info',
		default: 'info'
	};

	const iconName = $derived(iconNames[variant || 'default']);

	const close = () => {
		visible = false;
		setTimeout(() => {
			dispatch('close');
		}, 150); // Wait for animation
	};

	const handleClose = () => {
		if (timeoutId) {
			clearTimeout(timeoutId);
		}
		close();
	};

	onMount(() => {
		if (duration > 0) {
			timeoutId = setTimeout(() => {
				close();
			}, duration);
		}

		return () => {
			if (timeoutId) {
				clearTimeout(timeoutId);
			}
		};
	});
</script>

{#if visible}
	<div
		class={computedClass}
		class:animate-slide-up={visible}
		class:animate-fade-out={!visible}
		role="alert"
		aria-live="polite"
	>
		<div class="flex items-start space-x-3">
			{#if iconName}
				<Icon icon={iconName} class="h-5 w-5 mt-0.5 flex-shrink-0" />
			{/if}

			<div class="flex-1 space-y-1">
				{#if title}
					<div class="text-sm font-semibold">
						{title}
					</div>
				{/if}

				{#if description}
					<div class="text-sm opacity-90">
						{description}
					</div>
				{/if}

				<slot />
			</div>
		</div>

		{#if closable}
			<Button
				variant="ghost"
				size="icon"
				class="absolute right-2 top-2 h-6 w-6 rounded-md opacity-70 hover:opacity-100"
				onclick={handleClose}
				aria-label="Close notification"
			>
				<Icon icon="x" class="h-4 w-4" />
			</Button>
		{/if}
	</div>
{/if}

<style>
	.animate-fade-out {
		animation: fadeOut 150ms ease-out forwards;
	}

	@keyframes fadeOut {
		from {
			opacity: 1;
			transform: translateY(0);
		}
		to {
			opacity: 0;
			transform: translateY(-10px);
		}
	}
</style>
