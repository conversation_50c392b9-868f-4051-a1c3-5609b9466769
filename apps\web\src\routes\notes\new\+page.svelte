<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { Save, X, Settings, Tag, FolderOpen, AlertCircle, Clock } from 'lucide-svelte';
	// 簡單的通知系統
	let notifications: Array<{ id: number; type: 'success' | 'error'; message: string }> = [];
	let notificationId = 0;

	const showNotification = (type: 'success' | 'error', message: string) => {
		const id = ++notificationId;
		notifications = [...notifications, { id, type, message }];
		setTimeout(() => {
			notifications = notifications.filter(n => n.id !== id);
		}, 3000);
	};

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import MarkdownEditor from '$components/editor/MarkdownEditor.svelte';
	import { noteStore } from '$stores/note';

	// Note data
	let title = '';
	let content = '';
	let category = '';
	let tags: string[] = [];
	let priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium';
	let newTag = '';

	// Editor state
	let editor: MarkdownEditor;
	let isDirty = false;
	let isSaving = false;
	let lastSaved: Date | null = null;
	let autoSaveEnabled = true;
	let autoSaveInterval: NodeJS.Timeout | null = null;

	// Form validation
	$: isValid = title.trim().length > 0;
	$: canSave = isValid && isDirty && !isSaving;

	// Auto-save functionality
	const startAutoSave = () => {
		if (autoSaveInterval) {
			clearInterval(autoSaveInterval);
		}

		if (autoSaveEnabled) {
			autoSaveInterval = setInterval(() => {
				if (isDirty && isValid) {
					saveDraft();
				}
			}, 30000); // Auto-save every 30 seconds
		}
	};

	const stopAutoSave = () => {
		if (autoSaveInterval) {
			clearInterval(autoSaveInterval);
			autoSaveInterval = null;
		}
	};

	// Save as draft
	const saveDraft = async () => {
		if (!isValid || isSaving) return;

		isSaving = true;
		try {
			// TODO: Implement actual save to draft
			await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call

			isDirty = false;
			lastSaved = new Date();
			showNotification('success', '草稿已保存');
		} catch (error) {
			console.error('Failed to save draft:', error);
			showNotification('error', '保存草稿失敗');
		} finally {
			isSaving = false;
		}
	};

	// Save and publish
	const saveAndPublish = async () => {
		if (!isValid || isSaving) return;

		isSaving = true;
		try {
			// TODO: Implement actual save and publish
			await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

			showNotification('success', '筆記已發布');
			goto('/notes');
		} catch (error) {
			console.error('Failed to publish note:', error);
			showNotification('error', '發布筆記失敗');
		} finally {
			isSaving = false;
		}
	};

	// Handle content change
	const handleContentChange = (event: CustomEvent<{ value: string }>) => {
		content = event.detail.value;
		isDirty = true;
	};

	// Handle editor save (Ctrl+S)
	const handleEditorSave = () => {
		saveDraft();
	};

	// Add tag
	const addTag = () => {
		const tag = newTag.trim();
		if (tag && !tags.includes(tag)) {
			tags = [...tags, tag];
			newTag = '';
			isDirty = true;
		}
	};

	// Remove tag
	const removeTag = (tagToRemove: string) => {
		tags = tags.filter(tag => tag !== tagToRemove);
		isDirty = true;
	};

	// Handle tag input keydown
	const handleTagKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Enter') {
			event.preventDefault();
			addTag();
		}
	};

	// Handle title change
	const handleTitleChange = () => {
		isDirty = true;
	};

	// Handle category change
	const handleCategoryChange = () => {
		isDirty = true;
	};

	// Handle priority change
	const handlePriorityChange = () => {
		isDirty = true;
	};

	// Cancel and go back
	const handleCancel = () => {
		if (isDirty) {
			if (confirm('您有未保存的更改，確定要離開嗎？')) {
				goto('/notes');
			}
		} else {
			goto('/notes');
		}
	};

	// Keyboard shortcuts
	const handleKeydown = (event: KeyboardEvent) => {
		if (event.ctrlKey || event.metaKey) {
			switch (event.key) {
				case 's':
					event.preventDefault();
					saveDraft();
					break;
				case 'Enter':
					event.preventDefault();
					if (event.shiftKey) {
						saveAndPublish();
					}
					break;
			}
		}
	};

	// Lifecycle
	onMount(() => {
		startAutoSave();

		// Add global keyboard listener
		document.addEventListener('keydown', handleKeydown);

		// Warn before leaving with unsaved changes
		const handleBeforeUnload = (event: BeforeUnloadEvent) => {
			if (isDirty) {
				event.preventDefault();
				event.returnValue = '';
			}
		};

		window.addEventListener('beforeunload', handleBeforeUnload);

		return () => {
			stopAutoSave();
			document.removeEventListener('keydown', handleKeydown);
			window.removeEventListener('beforeunload', handleBeforeUnload);
		};
	});

	// Priority colors
	const priorityColors = {
		low: 'text-blue-600',
		medium: 'text-green-600',
		high: 'text-orange-600',
		urgent: 'text-red-600'
	};

	const priorityLabels = {
		low: '低',
		medium: '中',
		high: '高',
		urgent: '緊急'
	};
</script>

<svelte:head>
	<title>新增筆記 - Life Note</title>
</svelte:head>

<!-- 通知系統 -->
{#if notifications.length > 0}
	<div class="fixed top-4 right-4 z-50 space-y-2">
		{#each notifications as notification (notification.id)}
			<div
				class="px-4 py-2 rounded-md shadow-lg {notification.type === 'success'
					? 'bg-green-500 text-white'
					: 'bg-red-500 text-white'}"
			>
				{notification.message}
			</div>
		{/each}
	</div>
{/if}

<div class="note-editor h-full flex flex-col">
	<!-- Header -->
	<header class="flex-shrink-0 border-b border-border bg-background/95 backdrop-blur">
		<div class="container mx-auto px-4 py-3">
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-4">
					<h1 class="text-xl font-semibold">新增筆記</h1>

					{#if isDirty}
						<div class="flex items-center text-sm text-muted-foreground">
							<AlertCircle class="h-4 w-4 mr-1" />
							未保存的更改
						</div>
					{/if}

					{#if lastSaved}
						<div class="flex items-center text-sm text-muted-foreground">
							<Clock class="h-4 w-4 mr-1" />
							上次保存：{lastSaved.toLocaleTimeString()}
						</div>
					{/if}
				</div>

				<div class="flex items-center space-x-2">
					<Button
						variant="outline"
						size="sm"
						onclick={saveDraft}
						disabled={!canSave}
						class="relative"
					>
						{#if isSaving}
							<div class="spinner h-4 w-4 mr-2"></div>
						{:else}
							<Save class="h-4 w-4 mr-2" />
						{/if}
						保存草稿
					</Button>

					<Button
						variant="default"
						size="sm"
						onclick={saveAndPublish}
						disabled={!isValid || isSaving}
					>
						發布筆記
					</Button>

					<Button variant="ghost" size="sm" onclick={handleCancel}>
						<X class="h-4 w-4 mr-2" />
						取消
					</Button>
				</div>
			</div>
		</div>
	</header>

	<!-- Main content -->
	<div class="flex-1 flex overflow-hidden">
		<!-- Sidebar -->
		<aside class="w-80 border-r border-border bg-muted/30 p-4 overflow-y-auto">
			<div class="space-y-6">
				<!-- Basic Info -->
				<Card class="p-4">
					<h3 class="font-medium mb-3">基本信息</h3>

					<div class="space-y-3">
						<div>
							<label for="title" class="block text-sm font-medium mb-1">
								標題 <span class="text-destructive">*</span>
							</label>
							<input
								id="title"
								type="text"
								bind:value={title}
								oninput={handleTitleChange}
								placeholder="輸入筆記標題..."
								class="input"
								required
							/>
						</div>

						<div>
							<label for="category" class="block text-sm font-medium mb-1"> 分類 </label>
							<div class="relative">
								<FolderOpen
									class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
								/>
								<input
									id="category"
									type="text"
									bind:value={category}
									oninput={handleCategoryChange}
									placeholder="選擇或輸入分類..."
									class="input pl-10 pr-3"
								/>
							</div>
						</div>

						<div>
							<label for="priority" class="block text-sm font-medium mb-1"> 優先級 </label>
							<select
								id="priority"
								bind:value={priority}
								onchange={handlePriorityChange}
								class="select"
							>
								{#each Object.entries(priorityLabels) as [value, label]}
									<option {value}>{label}</option>
								{/each}
							</select>
						</div>
					</div>
				</Card>

				<!-- Tags -->
				<Card class="p-4">
					<h3 class="font-medium mb-3">標籤</h3>

					<div class="space-y-3">
						<div class="flex">
							<input
								type="text"
								bind:value={newTag}
								onkeydown={handleTagKeydown}
								placeholder="添加標籤..."
								class="flex-1 px-3 py-2 border border-input rounded-l-md focus:outline-none focus:ring-2 focus:ring-ring"
							/>
							<Button
								variant="outline"
								size="sm"
								onclick={addTag}
								class="rounded-l-none"
								disabled={!newTag.trim()}
							>
								<Tag class="h-4 w-4" />
							</Button>
						</div>

						{#if tags.length > 0}
							<div class="flex flex-wrap gap-2">
								{#each tags as tag}
									<span
										class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary"
									>
										{tag}
										<button onclick={() => removeTag(tag)} class="ml-1 hover:text-destructive">
											<X class="h-3 w-3" />
										</button>
									</span>
								{/each}
							</div>
						{/if}
					</div>
				</Card>

				<!-- Settings -->
				<Card class="p-4">
					<h3 class="font-medium mb-3">設置</h3>

					<div class="space-y-3">
						<label class="flex items-center space-x-2">
							<input
								type="checkbox"
								bind:checked={autoSaveEnabled}
								onchange={startAutoSave}
								class="rounded border-input"
							/>
							<span class="text-sm">自動保存</span>
						</label>
					</div>
				</Card>
			</div>
		</aside>

		<!-- Editor -->
		<main class="flex-1 overflow-hidden">
			<MarkdownEditor
				bind:this={editor}
				bind:value={content}
				onchange={handleContentChange}
				onsave={handleEditorSave}
				placeholder="開始寫作您的筆記..."
				autofocus={true}
				showToolbar={true}
				showPreview={false}
			/>
		</main>
	</div>
</div>

<style>
	.note-editor {
		height: calc(100vh - 3.5rem); /* Subtract header height */
	}

	/* Custom scrollbar for sidebar */
	aside::-webkit-scrollbar {
		width: 6px;
	}

	aside::-webkit-scrollbar-track {
		background: transparent;
	}

	aside::-webkit-scrollbar-thumb {
		background: hsl(var(--muted-foreground) / 0.3);
		border-radius: 3px;
	}

	aside::-webkit-scrollbar-thumb:hover {
		background: hsl(var(--muted-foreground) / 0.5);
	}
</style>
