<script lang="ts">
	import { onMount } from 'svelte';
	import { 
		Monitor, 
		Play, 
		Pause, 
		Settings, 
		Bell, 
		Clock,
		Target,
		AlertTriangle,
		CheckCircle,
		XCircle
	} from 'lucide-svelte';
	
	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { dependencyNotificationService } from '$lib/services/dependencyNotificationService';
	import { allNotes } from '$stores/notes';
	import type { NotificationSubscription } from '$lib/services/dependencyNotificationService';

	// 監控設置
	let isMonitoring = $state(false);
	let monitoringInterval = $state(30); // 秒
	let enableBrowserNotifications = $state(false);
	let enableSoundNotifications = $state(false);
	let notificationThreshold = $state(0.5); // 通知閾值

	// 訂閱管理
	let subscriptions: NotificationSubscription[] = $state([]);
	let selectedNoteId = $state('');
	let newSubscriptionDependencies: string[] = $state([]);

	// 統計信息
	let totalNotifications = $state(0);
	let unreadNotifications = $state(0);
	let monitoringUptime = $state(0);

	onMount(() => {
		loadSettings();
		loadSubscriptions();
		loadStatistics();
		
		// 檢查瀏覽器通知權限
		enableBrowserNotifications = Notification.permission === 'granted';
	});

	const loadSettings = () => {
		isMonitoring = dependencyNotificationService.isMonitoringActive();
		
		// 從 localStorage 載入設置
		const savedSettings = localStorage.getItem('dependency-monitoring-settings');
		if (savedSettings) {
			try {
				const settings = JSON.parse(savedSettings);
				monitoringInterval = settings.monitoringInterval || 30;
				enableSoundNotifications = settings.enableSoundNotifications || false;
				notificationThreshold = settings.notificationThreshold || 0.5;
			} catch (error) {
				console.error('Failed to load monitoring settings:', error);
			}
		}
	};

	const saveSettings = () => {
		const settings = {
			monitoringInterval,
			enableSoundNotifications,
			notificationThreshold
		};
		
		localStorage.setItem('dependency-monitoring-settings', JSON.stringify(settings));
	};

	const loadSubscriptions = () => {
		subscriptions = dependencyNotificationService.getSubscriptions();
	};

	const loadStatistics = () => {
		const notifications = dependencyNotificationService.getNotifications();
		totalNotifications = notifications.length;
		unreadNotifications = dependencyNotificationService.getUnreadNotifications().length;
	};

	const toggleMonitoring = () => {
		if (isMonitoring) {
			dependencyNotificationService.stopMonitoring();
			isMonitoring = false;
		} else {
			if ($allNotes && $allNotes.length > 0) {
				dependencyNotificationService.startMonitoring($allNotes, monitoringInterval * 1000);
				isMonitoring = true;
			} else {
				alert('沒有可監控的筆記');
			}
		}
		saveSettings();
	};

	const requestNotificationPermission = async () => {
		if (Notification.permission === 'default') {
			const permission = await Notification.requestPermission();
			enableBrowserNotifications = permission === 'granted';
		} else if (Notification.permission === 'granted') {
			enableBrowserNotifications = true;
		} else {
			alert('瀏覽器通知權限被拒絕，請在瀏覽器設置中允許通知');
		}
	};

	const addSubscription = () => {
		if (selectedNoteId) {
			dependencyNotificationService.subscribeToDependencyNotifications(
				selectedNoteId, 
				newSubscriptionDependencies
			);
			loadSubscriptions();
			selectedNoteId = '';
			newSubscriptionDependencies = [];
		}
	};

	const removeSubscription = (noteId: string) => {
		dependencyNotificationService.unsubscribeFromNotifications(noteId);
		loadSubscriptions();
	};

	const updateMonitoringInterval = () => {
		if (isMonitoring && $allNotes) {
			// 重新啟動監控以應用新的間隔
			dependencyNotificationService.stopMonitoring();
			dependencyNotificationService.startMonitoring($allNotes, monitoringInterval * 1000);
		}
		saveSettings();
	};

	const testNotification = () => {
		if (enableBrowserNotifications && Notification.permission === 'granted') {
			new Notification('Life Note - 測試通知', {
				body: '依賴關係監控系統正常運行',
				icon: '/favicon.ico'
			});
		}
		
		if (enableSoundNotifications) {
			// 播放通知聲音（這裡可以添加音頻播放邏輯）
			console.log('Playing notification sound...');
		}
	};

	const clearAllNotifications = () => {
		if (confirm('確定要清除所有通知嗎？')) {
			dependencyNotificationService.clearAllNotifications();
			loadStatistics();
		}
	};

	const formatUptime = (seconds: number) => {
		const hours = Math.floor(seconds / 3600);
		const minutes = Math.floor((seconds % 3600) / 60);
		return `${hours}h ${minutes}m`;
	};

	// 響應式更新監控間隔
	$effect(() => {
		if (monitoringInterval) {
			updateMonitoringInterval();
		}
	});
</script>

<div class="dependency-monitoring-settings space-y-6">
	<div class="flex items-center space-x-2">
		<Monitor class="h-6 w-6" />
		<h2 class="text-xl font-semibold">依賴關係監控設置</h2>
	</div>

	<!-- 監控狀態 -->
	<Card class="p-6">
		<div class="flex items-center justify-between mb-4">
			<h3 class="text-lg font-medium">監控狀態</h3>
			<div class="flex items-center space-x-2">
				{#if isMonitoring}
					<CheckCircle class="h-5 w-5 text-green-500" />
					<span class="text-green-600 font-medium">運行中</span>
				{:else}
					<XCircle class="h-5 w-5 text-red-500" />
					<span class="text-red-600 font-medium">已停止</span>
				{/if}
			</div>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
			<div class="text-center p-4 bg-muted/30 rounded-lg">
				<div class="text-2xl font-bold text-primary">{totalNotifications}</div>
				<div class="text-sm text-muted-foreground">總通知數</div>
			</div>
			<div class="text-center p-4 bg-muted/30 rounded-lg">
				<div class="text-2xl font-bold text-orange-500">{unreadNotifications}</div>
				<div class="text-sm text-muted-foreground">未讀通知</div>
			</div>
			<div class="text-center p-4 bg-muted/30 rounded-lg">
				<div class="text-2xl font-bold text-blue-500">{subscriptions.length}</div>
				<div class="text-sm text-muted-foreground">訂閱數量</div>
			</div>
		</div>

		<div class="flex items-center space-x-4">
			<Button on:click={toggleMonitoring} class="flex items-center space-x-2">
				{#if isMonitoring}
					<Pause class="h-4 w-4" />
					<span>停止監控</span>
				{:else}
					<Play class="h-4 w-4" />
					<span>開始監控</span>
				{/if}
			</Button>

			<Button variant="outline" on:click={testNotification}>
				<Bell class="h-4 w-4 mr-2" />
				測試通知
			</Button>

			<Button variant="outline" on:click={clearAllNotifications}>
				<AlertTriangle class="h-4 w-4 mr-2" />
				清除所有通知
			</Button>
		</div>
	</Card>

	<!-- 監控設置 -->
	<Card class="p-6">
		<h3 class="text-lg font-medium mb-4">監控設置</h3>
		
		<div class="space-y-4">
			<div>
				<label class="block text-sm font-medium mb-2">
					<Clock class="h-4 w-4 inline mr-1" />
					檢查間隔（秒）
				</label>
				<input
					type="number"
					bind:value={monitoringInterval}
					min="10"
					max="300"
					class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
				/>
				<p class="text-xs text-muted-foreground mt-1">
					建議設置為 30-60 秒，過短可能影響性能
				</p>
			</div>

			<div>
				<label class="block text-sm font-medium mb-2">
					<Target class="h-4 w-4 inline mr-1" />
					通知閾值
				</label>
				<input
					type="range"
					bind:value={notificationThreshold}
					min="0.1"
					max="1.0"
					step="0.1"
					class="w-full"
				/>
				<div class="flex justify-between text-xs text-muted-foreground">
					<span>低敏感度 (0.1)</span>
					<span>當前: {notificationThreshold}</span>
					<span>高敏感度 (1.0)</span>
				</div>
				<p class="text-xs text-muted-foreground mt-1">
					只有依賴關係強度變化超過此閾值才會發送通知
				</p>
			</div>
		</div>
	</Card>

	<!-- 通知設置 -->
	<Card class="p-6">
		<h3 class="text-lg font-medium mb-4">通知設置</h3>
		
		<div class="space-y-4">
			<div class="flex items-center justify-between">
				<div>
					<label class="font-medium">瀏覽器通知</label>
					<p class="text-sm text-muted-foreground">在瀏覽器中顯示桌面通知</p>
				</div>
				<div class="flex items-center space-x-2">
					{#if Notification.permission === 'granted'}
						<input
							type="checkbox"
							bind:checked={enableBrowserNotifications}
							class="rounded"
						/>
					{:else}
						<Button size="sm" on:click={requestNotificationPermission}>
							啟用
						</Button>
					{/if}
				</div>
			</div>

			<div class="flex items-center justify-between">
				<div>
					<label class="font-medium">聲音通知</label>
					<p class="text-sm text-muted-foreground">播放通知聲音</p>
				</div>
				<input
					type="checkbox"
					bind:checked={enableSoundNotifications}
					class="rounded"
				/>
			</div>
		</div>
	</Card>

	<!-- 訂閱管理 -->
	<Card class="p-6">
		<h3 class="text-lg font-medium mb-4">訂閱管理</h3>
		
		<!-- 添加新訂閱 -->
		<div class="mb-6 p-4 border rounded-lg">
			<h4 class="font-medium mb-3">添加新訂閱</h4>
			<div class="flex items-center space-x-2">
				<select
					bind:value={selectedNoteId}
					class="flex-1 px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
				>
					<option value="">選擇要監控的筆記</option>
					{#if $allNotes}
						{#each $allNotes as note}
							<option value={note.id}>{note.title}</option>
						{/each}
					{/if}
				</select>
				<Button on:click={addSubscription} disabled={!selectedNoteId}>
					添加訂閱
				</Button>
			</div>
		</div>

		<!-- 現有訂閱列表 -->
		<div class="space-y-2">
			<h4 class="font-medium">現有訂閱</h4>
			{#if subscriptions.length === 0}
				<p class="text-muted-foreground text-sm">暫無訂閱</p>
			{:else}
				{#each subscriptions as subscription}
					{@const note = $allNotes?.find(n => n.id === subscription.noteId)}
					<div class="flex items-center justify-between p-3 border rounded-lg">
						<div>
							<div class="font-medium">{note?.title || subscription.noteId}</div>
							<div class="text-sm text-muted-foreground">
								最後檢查: {subscription.lastChecked.toLocaleString()}
							</div>
						</div>
						<div class="flex items-center space-x-2">
							<span class="text-sm {subscription.enabled ? 'text-green-600' : 'text-red-600'}">
								{subscription.enabled ? '啟用' : '禁用'}
							</span>
							<Button 
								variant="outline" 
								size="sm" 
								on:click={() => removeSubscription(subscription.noteId)}
							>
								移除
							</Button>
						</div>
					</div>
				{/each}
			{/if}
		</div>
	</Card>
</div>
