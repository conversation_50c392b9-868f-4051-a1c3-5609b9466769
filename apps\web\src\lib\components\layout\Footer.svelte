<script lang="ts">
	import Icon from '@iconify/svelte';

	// App version and build info
	const version = '1.0.0';
	const buildTime = new Date().toISOString();
</script>

<footer class="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
	<div class="container flex flex-col items-center justify-between gap-4 py-6 md:flex-row">
		<!-- Left section -->
		<div class="flex flex-col items-center gap-2 md:flex-row md:gap-4">
			<p class="text-sm text-muted-foreground">
				© 2024 Life Note.
				<span class="inline-flex items-center">
					Made with
					<Icon icon="lucide:heart" class="mx-1 h-3 w-3 fill-red-500 text-red-500" />
					by Life Note Team
				</span>
			</p>

			<div class="flex items-center gap-2 text-xs text-muted-foreground">
				<span>v{version}</span>
				<span>•</span>
				<span title={`Built at ${buildTime}`}>
					{new Date(buildTime).toLocaleDateString('zh-TW')}
				</span>
			</div>
		</div>

		<!-- Right section -->
		<div class="flex items-center gap-4">
			<!-- Links -->
			<nav class="flex items-center gap-4 text-sm">
				<a href="/about" class="text-muted-foreground hover:text-foreground transition-colors">
					關於
				</a>
				<a href="/privacy" class="text-muted-foreground hover:text-foreground transition-colors">
					隱私政策
				</a>
				<a href="/terms" class="text-muted-foreground hover:text-foreground transition-colors">
					使用條款
				</a>
				<a
					href="https://github.com/your-org/life-note-client-vibe"
					target="_blank"
					rel="noopener noreferrer"
					class="inline-flex items-center text-muted-foreground hover:text-foreground transition-colors"
				>
					<Icon icon="lucide:github" class="mr-1 h-3 w-3" />
					GitHub
					<Icon icon="lucide:external-link" class="ml-1 h-2 w-2" />
				</a>
			</nav>
		</div>
	</div>

	<!-- Status bar (optional) -->
	<div class="border-t bg-muted/30">
		<div class="container flex items-center justify-between py-2 text-xs text-muted-foreground">
			<div class="flex items-center gap-4">
				<span class="flex items-center">
					<span class="mr-1 h-2 w-2 rounded-full bg-green-500"></span>
					系統正常
				</span>
				<span>本地存儲</span>
			</div>

			<div class="flex items-center gap-4">
				<span>Svelte + TypeScript</span>
				<span>Tauri Ready</span>
			</div>
		</div>
	</div>
</footer>

<style>
	/* Ensure footer stays at bottom */
	footer {
		margin-top: auto;
	}

	/* Smooth transitions */
	footer a {
		transition: color 0.2s ease-in-out;
	}

	/* Focus styles */
	footer a:focus-visible {
		outline: 2px solid hsl(var(--ring));
		outline-offset: 2px;
		border-radius: 2px;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		footer .container {
			text-align: center;
		}
	}
</style>
