<script>
  import { iconSvgs } from '$lib/utils/iconMapping';

  export let icon;
  export let size = 24;
  export let className = '';

  $: svgPath = iconSvgs[icon] || iconSvgs['x']; // fallback to 'x' icon
</script>

<svg 
  width={size} 
  height={size} 
  viewBox="0 0 24 24" 
  fill="none" 
  stroke="currentColor" 
  stroke-width="2" 
  stroke-linecap="round" 
  stroke-linejoin="round" 
  class={className}
>
  {@html svgPath}
</svg>
